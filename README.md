# 抖店售后审核退款系统

基于抖店开放平台SDK开发的售后审核退款管理系统，支持自动token刷新、售后列表查询和退款操作。

## 功能特性

- ✅ **自动Token管理**: 自动获取和刷新access_token，防止过期
- ✅ **售后列表查询**: 支持多条件筛选的售后订单列表
- ✅ **退款操作**: 支持同意退款、拒绝退款等操作
- ✅ **响应式界面**: 基于Bootstrap 5的现代化Web界面
- ✅ **操作日志**: 完整的操作日志记录
- ✅ **无数据库依赖**: 使用文件存储，部署简单

## 系统要求

- PHP 7.4+
- Web服务器 (Apache/Nginx)
- 抖店开放平台应用

## 安装部署

### 1. 下载代码

```bash
git clone <repository-url>
cd douyin-aftersale-system
```

### 2. 配置应用信息

编辑 `config/config.php` 文件，填入您的抖店开放平台应用信息：

```php
'douyin' => [
    'app_key' => 'your_app_key_here',        // 您的应用Key
    'app_secret' => 'your_app_secret_here',  // 您的应用Secret
    'shop_id' => 'your_shop_id_here',        // 您的店铺ID
    'redirect_uri' => 'http://localhost/callback.php', // 授权回调地址
],
```

### 3. 设置Web服务器

将 `public` 目录设置为Web根目录，或者将整个项目放在Web目录下，通过 `public/index.php` 访问。

### 4. 设置权限

确保以下目录可写：
```bash
chmod 755 storage/
chmod 755 logs/
```

## 使用说明

### 1. 首次授权

1. 访问系统首页
2. 点击"立即授权"按钮
3. 在抖店开放平台完成授权
4. 系统自动获取并保存token

### 2. 查看售后列表

- 系统会自动加载售后订单列表
- 支持按订单号、售后单号、状态等条件筛选
- 支持分页浏览

### 3. 处理退款申请

1. 在售后列表中找到需要处理的订单
2. 点击"同意"或"拒绝"按钮
3. 填写相关信息（退款金额、拒绝原因等）
4. 确认提交操作

## 目录结构

```
├── config/                 # 配置文件
│   └── config.php         # 主配置文件
├── src/                   # 源代码
│   └── Services/          # 服务类
│       ├── TokenManager.php      # Token管理类
│       └── AfterSaleService.php  # 售后服务类
├── public/                # Web根目录
│   ├── index.php         # 主页面
│   ├── api.php           # API接口
│   └── callback.php      # 授权回调
├── sdk-php/              # 抖店SDK
├── storage/              # 存储目录
│   └── tokens.json       # Token存储文件
├── logs/                 # 日志目录
└── README.md            # 说明文档
```

## API接口

### 获取售后列表
```
POST /api.php?action=list
```

参数：
- `order_id`: 订单号（可选）
- `aftersale_id`: 售后单号（可选）
- `aftersale_status`: 售后状态（可选）
- `aftersale_type`: 售后类型（可选）
- `page`: 页码（默认1）
- `size`: 每页数量（默认20）

### 执行售后操作
```
POST /api.php?action=operate
```

参数：
- `aftersale_id`: 售后单号（必填）
- `operation_type`: 操作类型（1=同意，2=拒绝，3=部分退款）
- `refund_amount`: 退款金额（同意退款时）
- `reject_reason`: 拒绝原因（拒绝退款时）
- `remark`: 备注（可选）

### 获取售后详情
```
GET /api.php?action=detail&aftersale_id=xxx
```

## 配置说明

### 售后状态映射
```php
'aftersale_status' => [
    1 => '待商家处理',
    2 => '商家同意退款',
    3 => '商家拒绝退款',
    4 => '买家申请平台介入',
    5 => '平台处理中',
    6 => '退款成功',
    7 => '退款失败',
    8 => '已撤销',
]
```

### 售后类型映射
```php
'aftersale_type' => [
    1 => '仅退款',
    2 => '退货退款',
    3 => '换货',
    4 => '维修',
]
```

## 注意事项

1. **Token安全**: token文件包含敏感信息，请确保 `storage/` 目录不可通过Web访问
2. **权限设置**: 确保Web服务器对 `storage/` 和 `logs/` 目录有写权限
3. **HTTPS**: 生产环境建议使用HTTPS协议
4. **备份**: 定期备份 `storage/tokens.json` 文件
5. **日志清理**: 定期清理 `logs/` 目录下的日志文件

## 故障排除

### 1. 授权失败
- 检查应用配置是否正确
- 确认回调地址是否正确设置
- 查看日志文件获取详细错误信息

### 2. Token过期
- 系统会自动刷新token，如果仍然失败，请重新授权
- 检查 `storage/tokens.json` 文件是否存在且可读写

### 3. API调用失败
- 检查网络连接
- 确认抖店开放平台服务状态
- 查看操作日志获取详细错误信息

## 开发说明

### 扩展功能
如需添加新功能，可以：
1. 在 `src/Services/` 目录下创建新的服务类
2. 在 `public/api.php` 中添加新的API接口
3. 在前端页面中调用新接口

### 自定义配置
可以在 `config/config.php` 中添加自定义配置项，然后在代码中使用。

## 许可证

本项目基于 MIT 许可证开源。

## 支持

如有问题或建议，请提交 Issue 或 Pull Request。