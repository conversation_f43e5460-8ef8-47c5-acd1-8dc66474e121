<?php
/**
 * 抖店售后审核退款系统配置文件
 */

return [
    // 数据库配置
    'database' => [
        'host' => 'localhost',
        'port' => 3306,
        'dbname' => 'douyin_aftersale',
        'username' => 'root',
        'password' => '',
        'charset' => 'utf8mb4',
        'options' => [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
        ]
    ],

    // 抖店开放平台配置
    'douyin' => [
        'app_key' => '7502292684338562600',
        'app_secret' => 'e27bbdd0-1946-448b-b139-21b9011690c2',
        'shop_id' => '228120078', // 默认店铺：短云游戏旗舰店
        'redirect_uri' => 'http://localhost:8000/callback.php',
        'api_base_url' => 'https://openapi-fxg.jinritemai.com',
    ],

    // 店铺列表配置
    'shops' => [
        '231044430' => [
            'name' => '三秒数字权益',
            'shop_id' => '231044430',
            'status' => 'active'
        ],
        '228120078' => [
            'name' => '短云游戏旗舰店',
            'shop_id' => '228120078',
            'status' => 'active'
        ],
        '203296334' => [
            'name' => '三秒权益',
            'shop_id' => '203296334',
            'status' => 'active'
        ]
    ],

    // 系统配置
    'system' => [
        'timezone' => 'Asia/Shanghai',
        'log_level' => 'info',
        'log_file' => __DIR__ . '/../logs/app.log',
        'session_timeout' => 3600, // 1小时
        'auto_refresh_token' => true, // 自动刷新token
        'token_refresh_threshold' => 300, // token过期前5分钟刷新
    ],

    // 分页配置
    'pagination' => [
        'default_page_size' => 20,
        'max_page_size' => 100,
    ],

    // 售后状态映射
    'aftersale_status' => [
        1 => '待商家处理',
        2 => '商家同意退款',
        3 => '商家拒绝退款',
        4 => '买家申请平台介入',
        5 => '平台处理中',
        6 => '退款成功',
        7 => '退款失败',
        8 => '已撤销',
        9 => '待买家确认',
        10 => '买家已确认',
        11 => '退款处理中',
        12 => '退款完成',
        13 => '已关闭',
        14 => '待平台处理',
        15 => '平台已处理',
        16 => '商家处理中',
        17 => '买家取消',
        18 => '系统关闭',
        19 => '待审核',
        20 => '审核通过',
        21 => '审核拒绝',
        22 => '待发货',
        23 => '已发货',
        24 => '已收货',
        25 => '已完成',
        26 => '已超时',
        27 => '已失效',
        28 => '退货退款完成',
        29 => '换货完成',
        30 => '维修完成',
    ],

    // 售后类型映射
    'aftersale_type' => [
        1 => '仅退款',
        2 => '退货退款',
        3 => '换货',
        4 => '维修',
    ],

    // 操作类型
    'operate_type' => [
        'agree' => 1,    // 同意退款
        'reject' => 2,   // 拒绝退款
        'partial' => 3,  // 部分退款
    ],
];