{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"API Request Started","data":{"method":"POST","action":"list","post_data":{"shop_id":"228120078","page":"0","size":"3"},"get_data":{"action":"list"},"user_agent":"curl\/8.7.1","ip":"::1"},"context":[],"memory_usage":"375.95 KB","peak_memory":"1.52 MB","execution_time":"3.76ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"Initializing Services","data":{"action":"list"},"context":[],"memory_usage":"376.14 KB","peak_memory":"1.52 MB","execution_time":"4ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[Token<PERSON>anager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"376.54 KB","peak_memory":"1.52 MB","execution_time":"4.07ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"378.21 KB","peak_memory":"1.52 MB","execution_time":"4.48ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[AfterSaleService] AfterSaleService Initialized","data":[],"context":[],"memory_usage":"377.91 KB","peak_memory":"1.52 MB","execution_time":"4.5ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"378.6 KB","peak_memory":"1.52 MB","execution_time":"4.53ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"Services Initialized Successfully","data":null,"context":[],"memory_usage":"378.04 KB","peak_memory":"1.52 MB","execution_time":"4.55ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"Handling AfterSale List Request","data":null,"context":[],"memory_usage":"378.04 KB","peak_memory":"1.52 MB","execution_time":"4.57ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"Processing AfterSale List Parameters","data":{"raw_post":{"shop_id":"228120078","page":"0","size":"3"}},"context":[],"memory_usage":"378.44 KB","peak_memory":"1.52 MB","execution_time":"4.59ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"Final Parameters for AfterSale Service","data":{"shop_id":"228120078","page":1,"size":3},"context":[],"memory_usage":"378.44 KB","peak_memory":"1.52 MB","execution_time":"4.61ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"Calling AfterSale Service","data":{"shop_id":"228120078"},"context":[],"memory_usage":"379.2 KB","peak_memory":"1.52 MB","execution_time":"4.63ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[AfterSaleService] Starting AfterSale List Request","data":{"shop_id":"228120078","params":{"shop_id":"228120078","page":1,"size":3}},"context":[],"memory_usage":"379.28 KB","peak_memory":"1.52 MB","execution_time":"4.65ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[TokenManager] Getting Valid Access Token","data":{"shop_id":"228120078"},"context":[],"memory_usage":"379.28 KB","peak_memory":"1.52 MB","execution_time":"4.67ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[TokenManager] Token Expiry Check","data":{"shop_id":"228120078","current_time":1755842046,"expires_at":1756226090,"time_to_expire":384044,"threshold":300,"needs_refresh":false},"context":[],"memory_usage":"380.13 KB","peak_memory":"1.52 MB","execution_time":"4.73ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[TokenManager] Token Still Valid","data":{"shop_id":"228120078","time_to_expire":384044},"context":[],"memory_usage":"380.13 KB","peak_memory":"1.52 MB","execution_time":"4.75ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[AfterSaleService] Access Token Retrieved","data":{"shop_id":"228120078","token_duration_ms":0.63,"token_preview":"lwa14vs9rj1kzyksvmma..."},"context":[],"memory_usage":"379.95 KB","peak_memory":"1.52 MB","execution_time":"5.31ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[AfterSaleService] API Request Parameters Set","data":{"shop_id":"228120078","page":0,"size":3,"has_standard_aftersale_status":false,"has_need_special_type":false},"context":[],"memory_usage":"380.71 KB","peak_memory":"1.52 MB","execution_time":"6.22ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[AfterSaleService] API Request Executed","data":{"shop_id":"228120078","api_duration_ms":477.94,"response_code":10000,"has_response":true},"context":[],"memory_usage":"422.71 KB","peak_memory":"1.52 MB","execution_time":"484.23ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[AfterSaleService] API Response Success","data":{"shop_id":"228120078","items_count":3,"total":97528,"has_more":true},"context":[],"memory_usage":"422.71 KB","peak_memory":"1.52 MB","execution_time":"484.58ms"}
{"timestamp":"2025-08-22 13:54:06","level":"PERFORMANCE","operation":"AfterSale Service Call","duration_ms":480.27,"memory_usage":"421.47 KB","peak_memory":"1.52 MB","data":{"shop_id":"228120078","result_success":true}}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"AfterSale List Result","data":{"shop_id":"228120078","list_count":3,"total":97528,"has_more":true},"context":[],"memory_usage":"421.47 KB","peak_memory":"1.52 MB","execution_time":"484.99ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"Response Data Prepared","data":{"response_size":8492,"pagination":{"current_page":1,"page_size":3,"total":97528,"total_pages":32510}},"context":[],"memory_usage":"422.2 KB","peak_memory":"1.52 MB","execution_time":"485.08ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"API Success","data":{"message":"获取成功","data_size":8492,"action":"list"},"context":[],"memory_usage":"422.23 KB","peak_memory":"1.52 MB","execution_time":"485.15ms"}
