{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"API Request Started","data":{"method":"POST","action":"list","post_data":{"shop_id":"228120078","page":"0","size":"3"},"get_data":{"action":"list"},"user_agent":"curl\/8.7.1","ip":"::1"},"context":[],"memory_usage":"375.95 KB","peak_memory":"1.52 MB","execution_time":"3.76ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"Initializing Services","data":{"action":"list"},"context":[],"memory_usage":"376.14 KB","peak_memory":"1.52 MB","execution_time":"4ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[Token<PERSON>anager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"376.54 KB","peak_memory":"1.52 MB","execution_time":"4.07ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"378.21 KB","peak_memory":"1.52 MB","execution_time":"4.48ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[AfterSaleService] AfterSaleService Initialized","data":[],"context":[],"memory_usage":"377.91 KB","peak_memory":"1.52 MB","execution_time":"4.5ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"378.6 KB","peak_memory":"1.52 MB","execution_time":"4.53ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"Services Initialized Successfully","data":null,"context":[],"memory_usage":"378.04 KB","peak_memory":"1.52 MB","execution_time":"4.55ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"Handling AfterSale List Request","data":null,"context":[],"memory_usage":"378.04 KB","peak_memory":"1.52 MB","execution_time":"4.57ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"Processing AfterSale List Parameters","data":{"raw_post":{"shop_id":"228120078","page":"0","size":"3"}},"context":[],"memory_usage":"378.44 KB","peak_memory":"1.52 MB","execution_time":"4.59ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"Final Parameters for AfterSale Service","data":{"shop_id":"228120078","page":1,"size":3},"context":[],"memory_usage":"378.44 KB","peak_memory":"1.52 MB","execution_time":"4.61ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"Calling AfterSale Service","data":{"shop_id":"228120078"},"context":[],"memory_usage":"379.2 KB","peak_memory":"1.52 MB","execution_time":"4.63ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[AfterSaleService] Starting AfterSale List Request","data":{"shop_id":"228120078","params":{"shop_id":"228120078","page":1,"size":3}},"context":[],"memory_usage":"379.28 KB","peak_memory":"1.52 MB","execution_time":"4.65ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[TokenManager] Getting Valid Access Token","data":{"shop_id":"228120078"},"context":[],"memory_usage":"379.28 KB","peak_memory":"1.52 MB","execution_time":"4.67ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[TokenManager] Token Expiry Check","data":{"shop_id":"228120078","current_time":1755842046,"expires_at":1756226090,"time_to_expire":384044,"threshold":300,"needs_refresh":false},"context":[],"memory_usage":"380.13 KB","peak_memory":"1.52 MB","execution_time":"4.73ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[TokenManager] Token Still Valid","data":{"shop_id":"228120078","time_to_expire":384044},"context":[],"memory_usage":"380.13 KB","peak_memory":"1.52 MB","execution_time":"4.75ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[AfterSaleService] Access Token Retrieved","data":{"shop_id":"228120078","token_duration_ms":0.63,"token_preview":"lwa14vs9rj1kzyksvmma..."},"context":[],"memory_usage":"379.95 KB","peak_memory":"1.52 MB","execution_time":"5.31ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[AfterSaleService] API Request Parameters Set","data":{"shop_id":"228120078","page":0,"size":3,"has_standard_aftersale_status":false,"has_need_special_type":false},"context":[],"memory_usage":"380.71 KB","peak_memory":"1.52 MB","execution_time":"6.22ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[AfterSaleService] API Request Executed","data":{"shop_id":"228120078","api_duration_ms":477.94,"response_code":10000,"has_response":true},"context":[],"memory_usage":"422.71 KB","peak_memory":"1.52 MB","execution_time":"484.23ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"[AfterSaleService] API Response Success","data":{"shop_id":"228120078","items_count":3,"total":97528,"has_more":true},"context":[],"memory_usage":"422.71 KB","peak_memory":"1.52 MB","execution_time":"484.58ms"}
{"timestamp":"2025-08-22 13:54:06","level":"PERFORMANCE","operation":"AfterSale Service Call","duration_ms":480.27,"memory_usage":"421.47 KB","peak_memory":"1.52 MB","data":{"shop_id":"228120078","result_success":true}}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"AfterSale List Result","data":{"shop_id":"228120078","list_count":3,"total":97528,"has_more":true},"context":[],"memory_usage":"421.47 KB","peak_memory":"1.52 MB","execution_time":"484.99ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"Response Data Prepared","data":{"response_size":8492,"pagination":{"current_page":1,"page_size":3,"total":97528,"total_pages":32510}},"context":[],"memory_usage":"422.2 KB","peak_memory":"1.52 MB","execution_time":"485.08ms"}
{"timestamp":"2025-08-22 13:54:06","level":"DEBUG","message":"API Success","data":{"message":"获取成功","data_size":8492,"action":"list"},"context":[],"memory_usage":"422.23 KB","peak_memory":"1.52 MB","execution_time":"485.15ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"API Request Started","data":{"method":"POST","action":"list","post_data":{"page":"0","size":"100","shop_id":"228120078","standard_aftersale_status":"[6]","need_special_type":"true"},"get_data":{"action":"list"},"user_agent":"Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","ip":"127.0.0.1"},"context":[],"memory_usage":"380.33 KB","peak_memory":"1.52 MB","execution_time":"6.67ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"Initializing Services","data":{"action":"list"},"context":[],"memory_usage":"380.84 KB","peak_memory":"1.52 MB","execution_time":"6.89ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"381.23 KB","peak_memory":"1.52 MB","execution_time":"6.94ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"382.91 KB","peak_memory":"1.52 MB","execution_time":"7.08ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"[AfterSaleService] AfterSaleService Initialized","data":[],"context":[],"memory_usage":"382.61 KB","peak_memory":"1.52 MB","execution_time":"7.11ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"383.3 KB","peak_memory":"1.52 MB","execution_time":"7.2ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"Services Initialized Successfully","data":null,"context":[],"memory_usage":"382.73 KB","peak_memory":"1.52 MB","execution_time":"7.26ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"Handling AfterSale List Request","data":null,"context":[],"memory_usage":"382.73 KB","peak_memory":"1.52 MB","execution_time":"7.29ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"Processing AfterSale List Parameters","data":{"raw_post":{"page":"0","size":"100","shop_id":"228120078","standard_aftersale_status":"[6]","need_special_type":"true"}},"context":[],"memory_usage":"383.13 KB","peak_memory":"1.52 MB","execution_time":"7.32ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"Standard AfterSale Status Decoded","data":{"raw":"[6]","decoded":[6]},"context":[],"memory_usage":"383.71 KB","peak_memory":"1.52 MB","execution_time":"7.38ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"Final Parameters for AfterSale Service","data":{"standard_aftersale_status":[6],"need_special_type":true,"shop_id":"228120078","page":1,"size":100},"context":[],"memory_usage":"383.34 KB","peak_memory":"1.52 MB","execution_time":"7.41ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"Calling AfterSale Service","data":{"shop_id":"228120078"},"context":[],"memory_usage":"384.11 KB","peak_memory":"1.52 MB","execution_time":"7.44ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"[AfterSaleService] Starting AfterSale List Request","data":{"shop_id":"228120078","params":{"standard_aftersale_status":[6],"need_special_type":true,"shop_id":"228120078","page":1,"size":100}},"context":[],"memory_usage":"384.19 KB","peak_memory":"1.52 MB","execution_time":"7.46ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"[TokenManager] Getting Valid Access Token","data":{"shop_id":"228120078"},"context":[],"memory_usage":"384.19 KB","peak_memory":"1.52 MB","execution_time":"7.49ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"[TokenManager] Token Expiry Check","data":{"shop_id":"228120078","current_time":1755842096,"expires_at":1756226090,"time_to_expire":383994,"threshold":300,"needs_refresh":false},"context":[],"memory_usage":"385.03 KB","peak_memory":"1.52 MB","execution_time":"7.61ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"[TokenManager] Token Still Valid","data":{"shop_id":"228120078","time_to_expire":383994},"context":[],"memory_usage":"385.03 KB","peak_memory":"1.52 MB","execution_time":"7.65ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"[AfterSaleService] Access Token Retrieved","data":{"shop_id":"228120078","token_duration_ms":0.47,"token_preview":"lwa14vs9rj1kzyksvmma..."},"context":[],"memory_usage":"384.85 KB","peak_memory":"1.52 MB","execution_time":"7.96ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"[AfterSaleService] API Request Parameters Set","data":{"shop_id":"228120078","page":0,"size":100,"has_standard_aftersale_status":true,"has_need_special_type":true},"context":[],"memory_usage":"385.62 KB","peak_memory":"1.52 MB","execution_time":"8.32ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"[AfterSaleService] API Request Executed","data":{"shop_id":"228120078","api_duration_ms":505.59,"response_code":10000,"has_response":true},"context":[],"memory_usage":"414.5 KB","peak_memory":"1.52 MB","execution_time":"513.97ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"[AfterSaleService] API Response Success","data":{"shop_id":"228120078","items_count":2,"total":2,"has_more":false},"context":[],"memory_usage":"414.5 KB","peak_memory":"1.52 MB","execution_time":"514.45ms"}
{"timestamp":"2025-08-22 13:54:56","level":"PERFORMANCE","operation":"AfterSale Service Call","duration_ms":507.66,"memory_usage":"413.26 KB","peak_memory":"1.52 MB","data":{"shop_id":"228120078","result_success":true}}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"AfterSale List Result","data":{"shop_id":"228120078","list_count":2,"total":2,"has_more":false},"context":[],"memory_usage":"413.26 KB","peak_memory":"1.52 MB","execution_time":"515.2ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"Response Data Prepared","data":{"response_size":5802,"pagination":{"current_page":1,"page_size":100,"total":2,"total_pages":1}},"context":[],"memory_usage":"413.99 KB","peak_memory":"1.52 MB","execution_time":"515.36ms"}
{"timestamp":"2025-08-22 13:54:56","level":"DEBUG","message":"API Success","data":{"message":"获取成功","data_size":5802,"action":"list"},"context":[],"memory_usage":"414.02 KB","peak_memory":"1.52 MB","execution_time":"515.51ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"API Request Started","data":{"method":"POST","action":"list","post_data":{"page":"0","size":"100","shop_id":"228120078","standard_aftersale_status":"[6]","need_special_type":"true"},"get_data":{"action":"list"},"user_agent":"Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","ip":"127.0.0.1"},"context":[],"memory_usage":"358.24 KB","peak_memory":"358.3 KB","execution_time":"0.21ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"Initializing Services","data":{"action":"list"},"context":[],"memory_usage":"358.44 KB","peak_memory":"362.55 KB","execution_time":"0.77ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"358.84 KB","peak_memory":"362.55 KB","execution_time":"0.95ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"360.32 KB","peak_memory":"362.95 KB","execution_time":"1.13ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"[AfterSaleService] AfterSaleService Initialized","data":[],"context":[],"memory_usage":"360.02 KB","peak_memory":"364.43 KB","execution_time":"1.23ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"360.71 KB","peak_memory":"364.43 KB","execution_time":"1.3ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"Services Initialized Successfully","data":null,"context":[],"memory_usage":"360.15 KB","peak_memory":"364.82 KB","execution_time":"1.37ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"Handling AfterSale List Request","data":null,"context":[],"memory_usage":"360.3 KB","peak_memory":"364.82 KB","execution_time":"1.44ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"Processing AfterSale List Parameters","data":{"raw_post":{"page":"0","size":"100","shop_id":"228120078","standard_aftersale_status":"[6]","need_special_type":"true"}},"context":[],"memory_usage":"360.7 KB","peak_memory":"364.82 KB","execution_time":"1.51ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"Standard AfterSale Status Decoded","data":{"raw":"[6]","decoded":[6]},"context":[],"memory_usage":"361.28 KB","peak_memory":"364.82 KB","execution_time":"1.59ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"Final Parameters for AfterSale Service","data":{"standard_aftersale_status":[6],"need_special_type":true,"shop_id":"228120078","page":1,"size":100},"context":[],"memory_usage":"360.91 KB","peak_memory":"364.82 KB","execution_time":"1.66ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"Calling AfterSale Service","data":{"shop_id":"228120078"},"context":[],"memory_usage":"361.68 KB","peak_memory":"365.02 KB","execution_time":"1.72ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"[AfterSaleService] Starting AfterSale List Request","data":{"shop_id":"228120078","params":{"standard_aftersale_status":[6],"need_special_type":true,"shop_id":"228120078","page":1,"size":100}},"context":[],"memory_usage":"361.76 KB","peak_memory":"365.02 KB","execution_time":"1.79ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"[TokenManager] Getting Valid Access Token","data":{"shop_id":"228120078"},"context":[],"memory_usage":"361.76 KB","peak_memory":"365.87 KB","execution_time":"1.86ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"[TokenManager] Token Expiry Check","data":{"shop_id":"228120078","current_time":1755842102,"expires_at":1756226090,"time_to_expire":383988,"threshold":300,"needs_refresh":false},"context":[],"memory_usage":"362.6 KB","peak_memory":"373.35 KB","execution_time":"2.01ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"[TokenManager] Token Still Valid","data":{"shop_id":"228120078","time_to_expire":383988},"context":[],"memory_usage":"362.6 KB","peak_memory":"373.35 KB","execution_time":"2.14ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"[AfterSaleService] Access Token Retrieved","data":{"shop_id":"228120078","token_duration_ms":0.41,"token_preview":"lwa14vs9rj1kzyksvmma..."},"context":[],"memory_usage":"362.22 KB","peak_memory":"373.35 KB","execution_time":"2.27ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"[AfterSaleService] API Request Parameters Set","data":{"shop_id":"228120078","page":0,"size":100,"has_standard_aftersale_status":true,"has_need_special_type":true},"context":[],"memory_usage":"362.78 KB","peak_memory":"373.35 KB","execution_time":"2.92ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"[AfterSaleService] API Request Executed","data":{"shop_id":"228120078","api_duration_ms":445.87,"response_code":10000,"has_response":true},"context":[],"memory_usage":"404.13 KB","peak_memory":"412.48 KB","execution_time":"449.06ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"[AfterSaleService] API Response Success","data":{"shop_id":"228120078","items_count":3,"total":3,"has_more":false},"context":[],"memory_usage":"404.03 KB","peak_memory":"412.48 KB","execution_time":"449.25ms"}
{"timestamp":"2025-08-22 13:55:02","level":"PERFORMANCE","operation":"AfterSale Service Call","duration_ms":447.62,"memory_usage":"402.79 KB","peak_memory":"412.48 KB","data":{"shop_id":"228120078","result_success":true}}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"AfterSale List Result","data":{"shop_id":"228120078","list_count":3,"total":3,"has_more":false},"context":[],"memory_usage":"402.79 KB","peak_memory":"412.48 KB","execution_time":"449.46ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"Response Data Prepared","data":{"response_size":8488,"pagination":{"current_page":1,"page_size":100,"total":3,"total_pages":1}},"context":[],"memory_usage":"403.52 KB","peak_memory":"414.54 KB","execution_time":"449.59ms"}
{"timestamp":"2025-08-22 13:55:02","level":"DEBUG","message":"API Success","data":{"message":"获取成功","data_size":8488,"action":"list"},"context":[],"memory_usage":"403.55 KB","peak_memory":"414.94 KB","execution_time":"449.68ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"API Request Started","data":{"method":"POST","action":"list","post_data":{"page":"0","size":"100","shop_id":"228120078","standard_aftersale_status":"[6]","need_special_type":"true"},"get_data":{"action":"list"},"user_agent":"Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","ip":"127.0.0.1"},"context":[],"memory_usage":"359.09 KB","peak_memory":"457.33 KB","execution_time":"1.25ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"Initializing Services","data":{"action":"list"},"context":[],"memory_usage":"359.13 KB","peak_memory":"457.33 KB","execution_time":"1.54ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"359.52 KB","peak_memory":"457.33 KB","execution_time":"1.67ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"361.01 KB","peak_memory":"457.33 KB","execution_time":"1.77ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"[AfterSaleService] AfterSaleService Initialized","data":[],"context":[],"memory_usage":"360.71 KB","peak_memory":"457.33 KB","execution_time":"1.8ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"361.4 KB","peak_memory":"457.33 KB","execution_time":"1.85ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"Services Initialized Successfully","data":null,"context":[],"memory_usage":"360.99 KB","peak_memory":"457.33 KB","execution_time":"1.87ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"Handling AfterSale List Request","data":null,"context":[],"memory_usage":"360.99 KB","peak_memory":"457.33 KB","execution_time":"1.9ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"Processing AfterSale List Parameters","data":{"raw_post":{"page":"0","size":"100","shop_id":"228120078","standard_aftersale_status":"[6]","need_special_type":"true"}},"context":[],"memory_usage":"361.39 KB","peak_memory":"457.33 KB","execution_time":"1.97ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"Standard AfterSale Status Decoded","data":{"raw":"[6]","decoded":[6]},"context":[],"memory_usage":"361.97 KB","peak_memory":"457.33 KB","execution_time":"2ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"Final Parameters for AfterSale Service","data":{"standard_aftersale_status":[6],"need_special_type":true,"shop_id":"228120078","page":1,"size":100},"context":[],"memory_usage":"361.6 KB","peak_memory":"457.33 KB","execution_time":"2.02ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"Calling AfterSale Service","data":{"shop_id":"228120078"},"context":[],"memory_usage":"362.37 KB","peak_memory":"457.33 KB","execution_time":"2.06ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"[AfterSaleService] Starting AfterSale List Request","data":{"shop_id":"228120078","params":{"standard_aftersale_status":[6],"need_special_type":true,"shop_id":"228120078","page":1,"size":100}},"context":[],"memory_usage":"362.45 KB","peak_memory":"457.33 KB","execution_time":"2.08ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"[TokenManager] Getting Valid Access Token","data":{"shop_id":"228120078"},"context":[],"memory_usage":"362.45 KB","peak_memory":"457.33 KB","execution_time":"2.12ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"[TokenManager] Token Expiry Check","data":{"shop_id":"228120078","current_time":1755842135,"expires_at":1756226090,"time_to_expire":383955,"threshold":300,"needs_refresh":false},"context":[],"memory_usage":"363.29 KB","peak_memory":"457.33 KB","execution_time":"2.29ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"[TokenManager] Token Still Valid","data":{"shop_id":"228120078","time_to_expire":383955},"context":[],"memory_usage":"363.29 KB","peak_memory":"457.33 KB","execution_time":"2.35ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"[AfterSaleService] Access Token Retrieved","data":{"shop_id":"228120078","token_duration_ms":0.31,"token_preview":"lwa14vs9rj1kzyksvmma..."},"context":[],"memory_usage":"362.91 KB","peak_memory":"457.33 KB","execution_time":"2.42ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"[AfterSaleService] API Request Parameters Set","data":{"shop_id":"228120078","page":0,"size":100,"has_standard_aftersale_status":true,"has_need_special_type":true},"context":[],"memory_usage":"363.47 KB","peak_memory":"457.33 KB","execution_time":"3.32ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"[AfterSaleService] API Request Executed","data":{"shop_id":"228120078","api_duration_ms":459.38,"response_code":10000,"has_response":true},"context":[],"memory_usage":"391.76 KB","peak_memory":"457.33 KB","execution_time":"462.82ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"[AfterSaleService] API Response Success","data":{"shop_id":"228120078","items_count":2,"total":2,"has_more":false},"context":[],"memory_usage":"391.66 KB","peak_memory":"457.33 KB","execution_time":"463.6ms"}
{"timestamp":"2025-08-22 13:55:35","level":"PERFORMANCE","operation":"AfterSale Service Call","duration_ms":462.09,"memory_usage":"390.42 KB","peak_memory":"457.33 KB","data":{"shop_id":"228120078","result_success":true}}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"AfterSale List Result","data":{"shop_id":"228120078","list_count":2,"total":2,"has_more":false},"context":[],"memory_usage":"390.42 KB","peak_memory":"457.33 KB","execution_time":"464.25ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"Response Data Prepared","data":{"response_size":5802,"pagination":{"current_page":1,"page_size":100,"total":2,"total_pages":1}},"context":[],"memory_usage":"391.16 KB","peak_memory":"457.33 KB","execution_time":"464.35ms"}
{"timestamp":"2025-08-22 13:55:35","level":"DEBUG","message":"API Success","data":{"message":"获取成功","data_size":5802,"action":"list"},"context":[],"memory_usage":"391.19 KB","peak_memory":"457.33 KB","execution_time":"464.43ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"API Request Started","data":{"method":"POST","action":"operate","post_data":{"aftersale_id":"147100082103746275","operation_type":"201"},"get_data":{"action":"operate"},"user_agent":"Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","ip":"127.0.0.1"},"context":[],"memory_usage":"357.84 KB","peak_memory":"357.9 KB","execution_time":"0.13ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"Initializing Services","data":{"action":"operate"},"context":[],"memory_usage":"358.03 KB","peak_memory":"361.95 KB","execution_time":"0.67ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"358.43 KB","peak_memory":"361.95 KB","execution_time":"0.8ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"359.91 KB","peak_memory":"362.54 KB","execution_time":"0.96ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[AfterSaleService] AfterSaleService Initialized","data":[],"context":[],"memory_usage":"359.62 KB","peak_memory":"364.02 KB","execution_time":"1.06ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"360.3 KB","peak_memory":"364.02 KB","execution_time":"1.15ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"Services Initialized Successfully","data":null,"context":[],"memory_usage":"359.74 KB","peak_memory":"364.41 KB","execution_time":"1.25ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"Handling AfterSale Operate Request","data":null,"context":[],"memory_usage":"359.9 KB","peak_memory":"364.41 KB","execution_time":"1.34ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[TokenManager] Getting Valid Access Token","data":{"shop_id":"228120078"},"context":[],"memory_usage":"360.34 KB","peak_memory":"364.41 KB","execution_time":"1.48ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[TokenManager] Token Expiry Check","data":{"shop_id":"228120078","current_time":1755842141,"expires_at":1756226090,"time_to_expire":383949,"threshold":300,"needs_refresh":false},"context":[],"memory_usage":"361.19 KB","peak_memory":"371.94 KB","execution_time":"1.64ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[TokenManager] Token Still Valid","data":{"shop_id":"228120078","time_to_expire":383949},"context":[],"memory_usage":"361.19 KB","peak_memory":"371.94 KB","execution_time":"1.73ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"API Success","data":{"message":"操作成功","data_size":91,"action":"operate"},"context":[],"memory_usage":"363.65 KB","peak_memory":"429.93 KB","execution_time":"271.31ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"API Request Started","data":{"method":"POST","action":"list","post_data":{"page":"0","size":"100","shop_id":"228120078","standard_aftersale_status":"[6]","need_special_type":"true"},"get_data":{"action":"list"},"user_agent":"Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","ip":"127.0.0.1"},"context":[],"memory_usage":"358.24 KB","peak_memory":"358.3 KB","execution_time":"0.04ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"Initializing Services","data":{"action":"list"},"context":[],"memory_usage":"358.44 KB","peak_memory":"362.35 KB","execution_time":"0.14ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"358.84 KB","peak_memory":"362.35 KB","execution_time":"0.21ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"360.32 KB","peak_memory":"362.95 KB","execution_time":"0.3ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[AfterSaleService] AfterSaleService Initialized","data":[],"context":[],"memory_usage":"360.02 KB","peak_memory":"364.43 KB","execution_time":"0.35ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"360.71 KB","peak_memory":"364.43 KB","execution_time":"0.41ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"Services Initialized Successfully","data":null,"context":[],"memory_usage":"360.15 KB","peak_memory":"364.82 KB","execution_time":"0.46ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"Handling AfterSale List Request","data":null,"context":[],"memory_usage":"360.3 KB","peak_memory":"364.82 KB","execution_time":"0.5ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"Processing AfterSale List Parameters","data":{"raw_post":{"page":"0","size":"100","shop_id":"228120078","standard_aftersale_status":"[6]","need_special_type":"true"}},"context":[],"memory_usage":"360.7 KB","peak_memory":"364.82 KB","execution_time":"0.55ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"Standard AfterSale Status Decoded","data":{"raw":"[6]","decoded":[6]},"context":[],"memory_usage":"361.28 KB","peak_memory":"364.82 KB","execution_time":"0.68ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"Final Parameters for AfterSale Service","data":{"standard_aftersale_status":[6],"need_special_type":true,"shop_id":"228120078","page":1,"size":100},"context":[],"memory_usage":"360.91 KB","peak_memory":"364.82 KB","execution_time":"0.86ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"Calling AfterSale Service","data":{"shop_id":"228120078"},"context":[],"memory_usage":"361.68 KB","peak_memory":"365.02 KB","execution_time":"0.94ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[AfterSaleService] Starting AfterSale List Request","data":{"shop_id":"228120078","params":{"standard_aftersale_status":[6],"need_special_type":true,"shop_id":"228120078","page":1,"size":100}},"context":[],"memory_usage":"361.76 KB","peak_memory":"365.02 KB","execution_time":"0.99ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[TokenManager] Getting Valid Access Token","data":{"shop_id":"228120078"},"context":[],"memory_usage":"361.76 KB","peak_memory":"365.87 KB","execution_time":"1.04ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[TokenManager] Token Expiry Check","data":{"shop_id":"228120078","current_time":1755842141,"expires_at":1756226090,"time_to_expire":383949,"threshold":300,"needs_refresh":false},"context":[],"memory_usage":"362.6 KB","peak_memory":"373.35 KB","execution_time":"1.16ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[TokenManager] Token Still Valid","data":{"shop_id":"228120078","time_to_expire":383949},"context":[],"memory_usage":"362.6 KB","peak_memory":"373.35 KB","execution_time":"1.19ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[AfterSaleService] Access Token Retrieved","data":{"shop_id":"228120078","token_duration_ms":0.25,"token_preview":"lwa14vs9rj1kzyksvmma..."},"context":[],"memory_usage":"362.22 KB","peak_memory":"373.35 KB","execution_time":"1.28ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[AfterSaleService] API Request Parameters Set","data":{"shop_id":"228120078","page":0,"size":100,"has_standard_aftersale_status":true,"has_need_special_type":true},"context":[],"memory_usage":"362.78 KB","peak_memory":"373.35 KB","execution_time":"1.71ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[AfterSaleService] API Request Executed","data":{"shop_id":"228120078","api_duration_ms":482.69,"response_code":10000,"has_response":true},"context":[],"memory_usage":"404.13 KB","peak_memory":"412.48 KB","execution_time":"484.48ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"[AfterSaleService] API Response Success","data":{"shop_id":"228120078","items_count":3,"total":3,"has_more":false},"context":[],"memory_usage":"404.03 KB","peak_memory":"412.48 KB","execution_time":"485.34ms"}
{"timestamp":"2025-08-22 13:55:41","level":"PERFORMANCE","operation":"AfterSale Service Call","duration_ms":485.03,"memory_usage":"402.79 KB","peak_memory":"412.48 KB","data":{"shop_id":"228120078","result_success":true}}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"AfterSale List Result","data":{"shop_id":"228120078","list_count":3,"total":3,"has_more":false},"context":[],"memory_usage":"402.79 KB","peak_memory":"412.48 KB","execution_time":"486.09ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"Response Data Prepared","data":{"response_size":8502,"pagination":{"current_page":1,"page_size":100,"total":3,"total_pages":1}},"context":[],"memory_usage":"403.52 KB","peak_memory":"414.54 KB","execution_time":"486.17ms"}
{"timestamp":"2025-08-22 13:55:41","level":"DEBUG","message":"API Success","data":{"message":"获取成功","data_size":8502,"action":"list"},"context":[],"memory_usage":"403.55 KB","peak_memory":"414.94 KB","execution_time":"486.24ms"}
{"timestamp":"2025-08-22 13:55:43","level":"DEBUG","message":"API Request Started","data":{"method":"POST","action":"operate","post_data":{"aftersale_id":"147100082103746275","operation_type":"201"},"get_data":{"action":"operate"},"user_agent":"Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","ip":"127.0.0.1"},"context":[],"memory_usage":"357.84 KB","peak_memory":"357.9 KB","execution_time":"0.27ms"}
{"timestamp":"2025-08-22 13:55:43","level":"DEBUG","message":"Initializing Services","data":{"action":"operate"},"context":[],"memory_usage":"358.03 KB","peak_memory":"361.95 KB","execution_time":"0.99ms"}
{"timestamp":"2025-08-22 13:55:43","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"358.43 KB","peak_memory":"361.95 KB","execution_time":"1.5ms"}
{"timestamp":"2025-08-22 13:55:43","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"359.91 KB","peak_memory":"362.54 KB","execution_time":"1.69ms"}
{"timestamp":"2025-08-22 13:55:43","level":"DEBUG","message":"[AfterSaleService] AfterSaleService Initialized","data":[],"context":[],"memory_usage":"359.62 KB","peak_memory":"364.02 KB","execution_time":"1.77ms"}
{"timestamp":"2025-08-22 13:55:43","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"360.3 KB","peak_memory":"364.02 KB","execution_time":"1.89ms"}
{"timestamp":"2025-08-22 13:55:43","level":"DEBUG","message":"Services Initialized Successfully","data":null,"context":[],"memory_usage":"359.74 KB","peak_memory":"364.41 KB","execution_time":"1.97ms"}
{"timestamp":"2025-08-22 13:55:43","level":"DEBUG","message":"Handling AfterSale Operate Request","data":null,"context":[],"memory_usage":"359.9 KB","peak_memory":"364.41 KB","execution_time":"2.05ms"}
{"timestamp":"2025-08-22 13:55:43","level":"DEBUG","message":"[TokenManager] Getting Valid Access Token","data":{"shop_id":"228120078"},"context":[],"memory_usage":"360.34 KB","peak_memory":"364.41 KB","execution_time":"2.14ms"}
{"timestamp":"2025-08-22 13:55:43","level":"DEBUG","message":"[TokenManager] Token Expiry Check","data":{"shop_id":"228120078","current_time":1755842143,"expires_at":1756226090,"time_to_expire":383947,"threshold":300,"needs_refresh":false},"context":[],"memory_usage":"361.19 KB","peak_memory":"371.94 KB","execution_time":"2.31ms"}
{"timestamp":"2025-08-22 13:55:43","level":"DEBUG","message":"[TokenManager] Token Still Valid","data":{"shop_id":"228120078","time_to_expire":383947},"context":[],"memory_usage":"361.19 KB","peak_memory":"371.94 KB","execution_time":"2.39ms"}
{"timestamp":"2025-08-22 13:55:43","level":"ERROR","message":"API Error","context":{"message":"操作失败: 售后操作失败: 操作失败","code":200,"action":"operate"}}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"API Request Started","data":{"method":"POST","action":"list","post_data":{"page":"0","size":"100","shop_id":"228120078","standard_aftersale_status":"[6]","need_special_type":"true"},"get_data":{"action":"list"},"user_agent":"Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","ip":"127.0.0.1"},"context":[],"memory_usage":"358.4 KB","peak_memory":"434.99 KB","execution_time":"0.19ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"Initializing Services","data":{"action":"list"},"context":[],"memory_usage":"358.44 KB","peak_memory":"434.99 KB","execution_time":"0.39ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"358.84 KB","peak_memory":"434.99 KB","execution_time":"0.52ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"360.32 KB","peak_memory":"434.99 KB","execution_time":"0.63ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"[AfterSaleService] AfterSaleService Initialized","data":[],"context":[],"memory_usage":"360.02 KB","peak_memory":"434.99 KB","execution_time":"0.67ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"360.71 KB","peak_memory":"434.99 KB","execution_time":"0.71ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"Services Initialized Successfully","data":null,"context":[],"memory_usage":"360.3 KB","peak_memory":"434.99 KB","execution_time":"0.74ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"Handling AfterSale List Request","data":null,"context":[],"memory_usage":"360.3 KB","peak_memory":"434.99 KB","execution_time":"0.77ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"Processing AfterSale List Parameters","data":{"raw_post":{"page":"0","size":"100","shop_id":"228120078","standard_aftersale_status":"[6]","need_special_type":"true"}},"context":[],"memory_usage":"360.7 KB","peak_memory":"434.99 KB","execution_time":"0.8ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"Standard AfterSale Status Decoded","data":{"raw":"[6]","decoded":[6]},"context":[],"memory_usage":"361.28 KB","peak_memory":"434.99 KB","execution_time":"0.86ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"Final Parameters for AfterSale Service","data":{"standard_aftersale_status":[6],"need_special_type":true,"shop_id":"228120078","page":1,"size":100},"context":[],"memory_usage":"360.91 KB","peak_memory":"434.99 KB","execution_time":"0.9ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"Calling AfterSale Service","data":{"shop_id":"228120078"},"context":[],"memory_usage":"361.68 KB","peak_memory":"434.99 KB","execution_time":"0.93ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"[AfterSaleService] Starting AfterSale List Request","data":{"shop_id":"228120078","params":{"standard_aftersale_status":[6],"need_special_type":true,"shop_id":"228120078","page":1,"size":100}},"context":[],"memory_usage":"361.76 KB","peak_memory":"434.99 KB","execution_time":"0.96ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"[TokenManager] Getting Valid Access Token","data":{"shop_id":"228120078"},"context":[],"memory_usage":"361.76 KB","peak_memory":"434.99 KB","execution_time":"0.99ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"[TokenManager] Token Expiry Check","data":{"shop_id":"228120078","current_time":1755842146,"expires_at":1756226090,"time_to_expire":383944,"threshold":300,"needs_refresh":false},"context":[],"memory_usage":"362.6 KB","peak_memory":"434.99 KB","execution_time":"1.05ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"[TokenManager] Token Still Valid","data":{"shop_id":"228120078","time_to_expire":383944},"context":[],"memory_usage":"362.6 KB","peak_memory":"434.99 KB","execution_time":"1.08ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"[AfterSaleService] Access Token Retrieved","data":{"shop_id":"228120078","token_duration_ms":0.15,"token_preview":"lwa14vs9rj1kzyksvmma..."},"context":[],"memory_usage":"362.22 KB","peak_memory":"434.99 KB","execution_time":"1.15ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"[AfterSaleService] API Request Parameters Set","data":{"shop_id":"228120078","page":0,"size":100,"has_standard_aftersale_status":true,"has_need_special_type":true},"context":[],"memory_usage":"362.78 KB","peak_memory":"434.99 KB","execution_time":"1.5ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"[AfterSaleService] API Request Executed","data":{"shop_id":"228120078","api_duration_ms":399.59,"response_code":10000,"has_response":true},"context":[],"memory_usage":"378.03 KB","peak_memory":"434.99 KB","execution_time":"401.15ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"[AfterSaleService] API Response Success","data":{"shop_id":"228120078","items_count":1,"total":1,"has_more":false},"context":[],"memory_usage":"377.94 KB","peak_memory":"434.99 KB","execution_time":"401.67ms"}
{"timestamp":"2025-08-22 13:55:46","level":"PERFORMANCE","operation":"AfterSale Service Call","duration_ms":401.08,"memory_usage":"376.7 KB","peak_memory":"434.99 KB","data":{"shop_id":"228120078","result_success":true}}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"AfterSale List Result","data":{"shop_id":"228120078","list_count":1,"total":1,"has_more":false},"context":[],"memory_usage":"376.7 KB","peak_memory":"434.99 KB","execution_time":"402.18ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"Response Data Prepared","data":{"response_size":3153,"pagination":{"current_page":1,"page_size":100,"total":1,"total_pages":1}},"context":[],"memory_usage":"377.43 KB","peak_memory":"434.99 KB","execution_time":"402.27ms"}
{"timestamp":"2025-08-22 13:55:46","level":"DEBUG","message":"API Success","data":{"message":"获取成功","data_size":3153,"action":"list"},"context":[],"memory_usage":"377.46 KB","peak_memory":"434.99 KB","execution_time":"402.35ms"}
{"timestamp":"2025-08-22 13:55:48","level":"DEBUG","message":"API Request Started","data":{"method":"POST","action":"operate","post_data":{"aftersale_id":"147101446475456309","operation_type":"201"},"get_data":{"action":"operate"},"user_agent":"Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","ip":"127.0.0.1"},"context":[],"memory_usage":"357.84 KB","peak_memory":"357.9 KB","execution_time":"0.08ms"}
{"timestamp":"2025-08-22 13:55:48","level":"DEBUG","message":"Initializing Services","data":{"action":"operate"},"context":[],"memory_usage":"358.03 KB","peak_memory":"361.95 KB","execution_time":"1.09ms"}
{"timestamp":"2025-08-22 13:55:48","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"358.43 KB","peak_memory":"361.95 KB","execution_time":"1.32ms"}
{"timestamp":"2025-08-22 13:55:48","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"359.91 KB","peak_memory":"362.54 KB","execution_time":"1.55ms"}
{"timestamp":"2025-08-22 13:55:48","level":"DEBUG","message":"[AfterSaleService] AfterSaleService Initialized","data":[],"context":[],"memory_usage":"359.62 KB","peak_memory":"364.02 KB","execution_time":"1.7ms"}
{"timestamp":"2025-08-22 13:55:48","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"360.3 KB","peak_memory":"364.02 KB","execution_time":"1.85ms"}
{"timestamp":"2025-08-22 13:55:48","level":"DEBUG","message":"Services Initialized Successfully","data":null,"context":[],"memory_usage":"359.74 KB","peak_memory":"364.41 KB","execution_time":"1.96ms"}
{"timestamp":"2025-08-22 13:55:48","level":"DEBUG","message":"Handling AfterSale Operate Request","data":null,"context":[],"memory_usage":"359.9 KB","peak_memory":"364.41 KB","execution_time":"2.07ms"}
{"timestamp":"2025-08-22 13:55:48","level":"DEBUG","message":"[TokenManager] Getting Valid Access Token","data":{"shop_id":"228120078"},"context":[],"memory_usage":"360.34 KB","peak_memory":"364.41 KB","execution_time":"2.17ms"}
{"timestamp":"2025-08-22 13:55:48","level":"DEBUG","message":"[TokenManager] Token Expiry Check","data":{"shop_id":"228120078","current_time":1755842148,"expires_at":1756226090,"time_to_expire":383942,"threshold":300,"needs_refresh":false},"context":[],"memory_usage":"361.19 KB","peak_memory":"371.94 KB","execution_time":"2.31ms"}
{"timestamp":"2025-08-22 13:55:48","level":"DEBUG","message":"[TokenManager] Token Still Valid","data":{"shop_id":"228120078","time_to_expire":383942},"context":[],"memory_usage":"361.19 KB","peak_memory":"371.94 KB","execution_time":"2.39ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"API Success","data":{"message":"操作成功","data_size":91,"action":"operate"},"context":[],"memory_usage":"362.71 KB","peak_memory":"371.94 KB","execution_time":"308.91ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"API Request Started","data":{"method":"POST","action":"list","post_data":{"page":"0","size":"100","shop_id":"228120078","standard_aftersale_status":"[6]","need_special_type":"true"},"get_data":{"action":"list"},"user_agent":"Mozilla\/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/********* Safari\/537.36 Edg\/*********","ip":"127.0.0.1"},"context":[],"memory_usage":"358.24 KB","peak_memory":"358.3 KB","execution_time":"0.15ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"Initializing Services","data":{"action":"list"},"context":[],"memory_usage":"358.44 KB","peak_memory":"362.35 KB","execution_time":"0.39ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"358.84 KB","peak_memory":"362.35 KB","execution_time":"0.55ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"360.32 KB","peak_memory":"362.95 KB","execution_time":"0.68ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"[AfterSaleService] AfterSaleService Initialized","data":[],"context":[],"memory_usage":"360.02 KB","peak_memory":"364.43 KB","execution_time":"0.75ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"[TokenManager] TokenManager Initialized","data":{"app_key":"7502292684...","api_base_url":"https:\/\/openapi-fxg.jinritemai.com"},"context":[],"memory_usage":"360.71 KB","peak_memory":"364.43 KB","execution_time":"0.81ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"Services Initialized Successfully","data":null,"context":[],"memory_usage":"360.15 KB","peak_memory":"364.82 KB","execution_time":"0.86ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"Handling AfterSale List Request","data":null,"context":[],"memory_usage":"360.3 KB","peak_memory":"364.82 KB","execution_time":"0.91ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"Processing AfterSale List Parameters","data":{"raw_post":{"page":"0","size":"100","shop_id":"228120078","standard_aftersale_status":"[6]","need_special_type":"true"}},"context":[],"memory_usage":"360.7 KB","peak_memory":"364.82 KB","execution_time":"0.97ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"Standard AfterSale Status Decoded","data":{"raw":"[6]","decoded":[6]},"context":[],"memory_usage":"361.28 KB","peak_memory":"364.82 KB","execution_time":"1.11ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"Final Parameters for AfterSale Service","data":{"standard_aftersale_status":[6],"need_special_type":true,"shop_id":"228120078","page":1,"size":100},"context":[],"memory_usage":"360.91 KB","peak_memory":"364.82 KB","execution_time":"1.18ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"Calling AfterSale Service","data":{"shop_id":"228120078"},"context":[],"memory_usage":"361.68 KB","peak_memory":"365.02 KB","execution_time":"1.25ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"[AfterSaleService] Starting AfterSale List Request","data":{"shop_id":"228120078","params":{"standard_aftersale_status":[6],"need_special_type":true,"shop_id":"228120078","page":1,"size":100}},"context":[],"memory_usage":"361.76 KB","peak_memory":"365.02 KB","execution_time":"1.3ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"[TokenManager] Getting Valid Access Token","data":{"shop_id":"228120078"},"context":[],"memory_usage":"361.76 KB","peak_memory":"365.87 KB","execution_time":"1.35ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"[TokenManager] Token Expiry Check","data":{"shop_id":"228120078","current_time":1755842149,"expires_at":1756226090,"time_to_expire":383941,"threshold":300,"needs_refresh":false},"context":[],"memory_usage":"362.6 KB","peak_memory":"373.35 KB","execution_time":"1.46ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"[TokenManager] Token Still Valid","data":{"shop_id":"228120078","time_to_expire":383941},"context":[],"memory_usage":"362.6 KB","peak_memory":"373.35 KB","execution_time":"1.51ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"[AfterSaleService] Access Token Retrieved","data":{"shop_id":"228120078","token_duration_ms":0.3,"token_preview":"lwa14vs9rj1kzyksvmma..."},"context":[],"memory_usage":"362.22 KB","peak_memory":"373.35 KB","execution_time":"1.65ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"[AfterSaleService] API Request Parameters Set","data":{"shop_id":"228120078","page":0,"size":100,"has_standard_aftersale_status":true,"has_need_special_type":true},"context":[],"memory_usage":"362.78 KB","peak_memory":"373.35 KB","execution_time":"2.36ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"[AfterSaleService] API Request Executed","data":{"shop_id":"228120078","api_duration_ms":310.03,"response_code":10000,"has_response":true},"context":[],"memory_usage":"391.09 KB","peak_memory":"399.44 KB","execution_time":"312.59ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"[AfterSaleService] API Response Success","data":{"shop_id":"228120078","items_count":2,"total":2,"has_more":false},"context":[],"memory_usage":"390.99 KB","peak_memory":"399.44 KB","execution_time":"313.39ms"}
{"timestamp":"2025-08-22 13:55:49","level":"PERFORMANCE","operation":"AfterSale Service Call","duration_ms":312.51,"memory_usage":"389.75 KB","peak_memory":"399.44 KB","data":{"shop_id":"228120078","result_success":true}}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"AfterSale List Result","data":{"shop_id":"228120078","list_count":2,"total":2,"has_more":false},"context":[],"memory_usage":"389.75 KB","peak_memory":"399.44 KB","execution_time":"313.91ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"Response Data Prepared","data":{"response_size":5846,"pagination":{"current_page":1,"page_size":100,"total":2,"total_pages":1}},"context":[],"memory_usage":"390.48 KB","peak_memory":"399.44 KB","execution_time":"314ms"}
{"timestamp":"2025-08-22 13:55:49","level":"DEBUG","message":"API Success","data":{"message":"获取成功","data_size":5846,"action":"list"},"context":[],"memory_usage":"390.52 KB","peak_memory":"399.44 KB","execution_time":"314.19ms"}
