<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入配置和服务类
require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../src/Services/TokenManager.php';
require_once __DIR__ . '/../../src/Services/AfterSaleService.php';

$config = require __DIR__ . '/../../config/config.php';

// 设置时区
date_default_timezone_set($config['system']['timezone']);

// 错误处理函数
function sendError($message, $code = 400) {
    http_response_code($code);
    echo json_encode([
        'success' => false,
        'message' => $message,
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

function sendSuccess($data = null, $message = '操作成功') {
    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => $data,
        'timestamp' => time()
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 只处理POST请求
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendError('只支持POST请求', 405);
    }

    // 获取参数
    $aftersaleId = $_POST['aftersale_id'] ?? '';
    $operationType = $_POST['operation_type'] ?? '';
    $refundAmount = $_POST['refund_amount'] ?? null;
    $rejectReason = $_POST['reject_reason'] ?? '';

    // 验证必需参数
    if (empty($aftersaleId)) {
        sendError('售后单号不能为空');
    }

    if (empty($operationType)) {
        sendError('操作类型不能为空');
    }

    // 验证操作类型
    if (!in_array($operationType, ['201', '202'])) {
        sendError('无效的操作类型');
    }

    // 转换操作类型：201->1（同意），202->2（拒绝）
    $internalOperationType = $operationType == '201' ? 1 : 2;

    // 同意退款时验证退款金额
    if ($operationType == '201' && (empty($refundAmount) || !is_numeric($refundAmount) || $refundAmount <= 0)) {
        sendError('同意退款时必须提供有效的退款金额');
    }

    // 拒绝退款时验证拒绝原因
    if ($operationType == '202' && empty($rejectReason)) {
        sendError('拒绝退款时必须提供拒绝原因');
    }

    // 初始化服务
    $tokenManager = new TokenManager($config);
    $afterSaleService = new AfterSaleService($config, $tokenManager);

    // 构建操作数据
    $operationData = [];

    // 添加额外参数
    if ($operationType == '201' && $refundAmount) {
        $operationData['refund_amount'] = (int)$refundAmount;
    }

    if ($operationType == '202' && $rejectReason) {
        $operationData['reject_reason'] = $rejectReason;
    }

    // 调用售后操作接口
    $result = $afterSaleService->operateAfterSale($aftersaleId, $internalOperationType, $operationData);

    if ($result && $result['success']) {
        $operationText = $operationType == '201' ? '同意退款' : '拒绝退款';
        sendSuccess($result['data'] ?? null, $operationText . '成功');
    } else {
        $errorMessage = $result['message'] ?? '操作失败';
        sendError($errorMessage);
    }

} catch (Exception $e) {
    error_log('售后操作错误: ' . $e->getMessage());
    sendError('系统错误: ' . $e->getMessage(), 500);
}
?>
