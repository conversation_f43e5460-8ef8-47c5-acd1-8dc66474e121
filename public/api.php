<?php
// 记录开始时间用于性能分析
define('APP_START_TIME', microtime(true));

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// 引入配置和服务类
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../src/Services/TokenManager.php';
require_once __DIR__ . '/../src/Services/AfterSaleService.php';
require_once __DIR__ . '/../src/Services/OrderService.php';
require_once __DIR__ . '/../src/Services/DebugLogger.php';

$config = require __DIR__ . '/../config/config.php';

// 设置时区
date_default_timezone_set($config['system']['timezone']);

// 初始化调试器
$debugLogger = new DebugLogger($config);

// 开启错误显示（如果在调试模式）
if ($config['system']['debug_mode'] ?? false) {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}

// 记录请求开始
$debugLogger->debug('API Request Started', [
    'method' => $_SERVER['REQUEST_METHOD'],
    'action' => $_GET['action'] ?? 'unknown',
    'post_data' => $_POST,
    'get_data' => $_GET,
    'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
    'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
]);

// 错误处理函数
function sendError($message, $code = 200) {
    global $debugLogger, $config;

    $debugLogger->error('API Error', null, [
        'message' => $message,
        'code' => $code,
        'action' => $_GET['action'] ?? 'unknown'
    ]);

    http_response_code($code);

    $response = [
        'success' => false,
        'message' => $message,
        'timestamp' => time()
    ];

    // 在调试模式下添加额外信息
    if ($config['system']['debug_mode'] ?? false) {
        $response['debug'] = [
            'execution_time' => round((microtime(true) - APP_START_TIME) * 1000, 2) . 'ms',
            'memory_usage' => formatBytes(memory_get_usage()),
            'peak_memory' => formatBytes(memory_get_peak_usage()),
            'request_id' => uniqid()
        ];
    }

    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 成功响应函数
function sendSuccess($data = null, $message = 'success') {
    global $debugLogger, $config;

    $debugLogger->debug('API Success', [
        'message' => $message,
        'data_size' => $data ? strlen(json_encode($data)) : 0,
        'action' => $_GET['action'] ?? 'unknown'
    ]);

    $response = [
        'success' => true,
        'message' => $message,
        'data' => $data,
        'timestamp' => time()
    ];

    // 在调试模式下添加额外信息
    if ($config['system']['debug_mode'] ?? false) {
        $response['debug'] = [
            'execution_time' => round((microtime(true) - APP_START_TIME) * 1000, 2) . 'ms',
            'memory_usage' => formatBytes(memory_get_usage()),
            'peak_memory' => formatBytes(memory_get_peak_usage()),
            'request_id' => uniqid()
        ];
    }

    echo json_encode($response, JSON_UNESCAPED_UNICODE);
    exit;
}

// 格式化字节数的辅助函数
function formatBytes($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    $bytes /= pow(1024, $pow);
    return round($bytes, 2) . ' ' . $units[$pow];
}

try {
    $action = $_GET['action'] ?? '';

    $debugLogger->debug('Initializing Services', ['action' => $action]);

    $tokenManager = new TokenManager($config);
    $afterSaleService = new AfterSaleService($config);
    $orderService = new OrderService($config);

    $debugLogger->debug('Services Initialized Successfully');

    switch ($action) {
        case 'list':
            // 获取售后列表
            $debugLogger->debug('Handling AfterSale List Request');
            handleAfterSaleList($afterSaleService);
            break;

        case 'operate':
            // 执行售后操作
            $debugLogger->debug('Handling AfterSale Operate Request');
            handleAfterSaleOperate($afterSaleService);
            break;

        case 'detail':
            // 获取售后详情
            $debugLogger->debug('Handling AfterSale Detail Request');
            handleAfterSaleDetail($afterSaleService);
            break;

        case 'searchList':
            // 搜索订单列表
            $debugLogger->debug('Handling Order Search List Request');
            handleOrderSearchList($orderService);
            break;

        default:
            $debugLogger->debug('Invalid Action', ['action' => $action]);
            sendError('无效的操作类型');
    }

} catch (Exception $e) {
    $debugLogger->error('API Exception', $e, [
        'action' => $_GET['action'] ?? 'unknown',
        'post_data' => $_POST,
        'get_data' => $_GET
    ]);
    sendError($e->getMessage(), 500);
}

/**
 * 处理售后列表请求
 */
function handleAfterSaleList($afterSaleService) {
    global $debugLogger;

    try {
        $startTime = microtime(true);
        $params = [];

        $debugLogger->debug('Processing AfterSale List Parameters', ['raw_post' => $_POST]);

        // 获取筛选参数
        if (!empty($_POST['order_id'])) {
            $params['order_id'] = trim($_POST['order_id']);
        }
        if (!empty($_POST['aftersale_id'])) {
            $params['aftersale_id'] = trim($_POST['aftersale_id']);
        }
        if (!empty($_POST['aftersale_status'])) {
            $params['aftersale_status'] = intval($_POST['aftersale_status']);
        }
        if (!empty($_POST['standard_aftersale_status'])) {
            $decoded = json_decode($_POST['standard_aftersale_status'], true);
            if ($decoded !== null) {
                $params['standard_aftersale_status'] = $decoded;
                $debugLogger->debug('Standard AfterSale Status Decoded', [
                    'raw' => $_POST['standard_aftersale_status'],
                    'decoded' => $decoded
                ]);
            }
        }
        if (!empty($_POST['need_special_type'])) {
            $params['need_special_type'] = $_POST['need_special_type'] === 'true' || $_POST['need_special_type'] === true;
        }
        if (!empty($_POST['aftersale_type'])) {
            $params['aftersale_type'] = intval($_POST['aftersale_type']);
        }
        if (!empty($_POST['start_time'])) {
            $params['start_time'] = strtotime($_POST['start_time']);
        }
        if (!empty($_POST['end_time'])) {
            $params['end_time'] = strtotime($_POST['end_time']);
        }
        if (!empty($_POST['shop_id'])) {
            $params['shop_id'] = trim($_POST['shop_id']);
        }

        // 分页参数
        $params['page'] = max(1, intval($_POST['page'] ?? 1));
        $params['size'] = min(100, max(1, intval($_POST['size'] ?? 20)));

        $debugLogger->debug('Final Parameters for AfterSale Service', $params);

        // 调试：直接在响应中显示参数信息
        $debug_info = [
            'received_post' => $_POST,
            'processed_params' => $params,
            'has_standard_aftersale_status' => isset($_POST['standard_aftersale_status']),
            'has_need_special_type' => isset($_POST['need_special_type']),
            'parameter_processing_time' => round((microtime(true) - $startTime) * 1000, 2) . 'ms'
        ];

        $debugLogger->debug('Calling AfterSale Service', ['shop_id' => $params['shop_id'] ?? 'unknown']);
        $serviceStartTime = microtime(true);

        $result = $afterSaleService->getAfterSaleList($params);

        $debugLogger->performance('AfterSale Service Call', $serviceStartTime, [
            'shop_id' => $params['shop_id'] ?? 'unknown',
            'result_success' => $result['success'] ?? false
        ]);

        if ($result['success']) {
            // 格式化返回数据
            $listData = $result['data']->items ?? [];
            $shopId = $params['shop_id'] ?? 'unknown';

            $debugLogger->debug('AfterSale List Result', [
                'shop_id' => $shopId,
                'list_count' => count($listData),
                'total' => $result['data']->total ?? 0,
                'has_more' => $result['data']->has_more ?? false
            ]);

            $responseData = [
                'list' => $listData,
                'total' => $result['data']->total ?? 0,
                'has_more' => $result['data']->has_more ?? false
            ];

            // 添加分页信息 - 转换为前端显示格式（从1开始）
            $apiPage = $result['data']->page ?? $result['page'] ?? 0;
            $pageSize = $result['data']->size ?? $result['size'] ?? 20;
            $total = $result['data']->total ?? 0;

            $pagination = [
                'current_page' => $apiPage + 1, // 转换为从1开始
                'page_size' => $pageSize,
                'total' => $total,
                'total_pages' => ceil($total / $pageSize)
            ];

            $responseData['pagination'] = $pagination;
            $responseData['debug'] = $debug_info; // 添加调试信息

            $debugLogger->debug('Response Data Prepared', [
                'response_size' => strlen(json_encode($responseData)),
                'pagination' => $pagination
            ]);

            sendSuccess($responseData, '获取成功');
        } else {
            $debugLogger->error('AfterSale Service Failed', null, [
                'result' => $result,
                'params' => $params
            ]);
            sendError('获取售后列表失败');
        }

    } catch (Exception $e) {
        // 如果是token相关错误，尝试获取授权URL
        if (strpos($e->getMessage(), 'token') !== false || strpos($e->getMessage(), '授权') !== false) {
            global $tokenManager;
            $authUrl = $tokenManager->getAuthUrl();
            sendError('需要重新授权: ' . $e->getMessage() . ' 授权地址: ' . $authUrl);
        } else {
            sendError('获取售后列表失败: ' . $e->getMessage());
        }
    }
}

/**
 * 处理售后操作请求
 */
function handleAfterSaleOperate($afterSaleService) {
    try {
        $aftersaleId = trim($_POST['aftersale_id'] ?? '');
        $operationType = intval($_POST['operation_type'] ?? 0);

        if (empty($aftersaleId)) {
            sendError('售后单号不能为空');
        }

        if (!in_array($operationType, [201, 202])) {
            sendError('无效的操作类型');
        }

        $operationData = [];

        // 根据操作类型设置相应数据
        if ($operationType == 201) { // 同意退款
            if (!empty($_POST['refund_amount'])) {
                $operationData['refund_amount'] = intval($_POST['refund_amount']); // 转换为分
            }
        }

        if ($operationType == 202) { // 拒绝退款
            $operationData['reject_reason'] = trim($_POST['reject_reason'] ?? '商家拒绝退款');
        }

        if (!empty($_POST['remark'])) {
            $operationData['remark'] = trim($_POST['remark']);
        }

        if (!empty($_POST['store_id'])) {
            $operationData['store_id'] = trim($_POST['store_id']);
        }

        $result = $afterSaleService->operateAfterSale($aftersaleId, $operationType, $operationData);

        if ($result['success']) {
            sendSuccess($result['data'], $result['message']);
        } else {
            sendError('操作失败');
        }

    } catch (Exception $e) {
        sendError('操作失败: ' . $e->getMessage());
    }
}

/**
 * 处理售后详情请求
 */
function handleAfterSaleDetail($afterSaleService) {
    try {
        $aftersaleId = trim($_GET['aftersale_id'] ?? '');

        if (empty($aftersaleId)) {
            sendError('售后单号不能为空');
        }

        $result = $afterSaleService->getAfterSaleDetail($aftersaleId);

        if ($result['success']) {
            sendSuccess($result['data'], '获取成功');
        } else {
            sendError('获取售后详情失败');
        }

    } catch (Exception $e) {
        sendError('获取售后详情失败: ' . $e->getMessage());
    }
}

/**
 * 处理订单搜索列表请求
 */
function handleOrderSearchList($orderService) {
    try {
        // 获取请求参数
        $authorId = trim($_POST['author_id'] ?? $_GET['author_id'] ?? '');
        $shopId = trim($_POST['shop_id'] ?? $_GET['shop_id'] ?? '');
        $page = intval($_POST['page'] ?? $_GET['page'] ?? 0);
        $size = intval($_POST['size'] ?? $_GET['size'] ?? 100);

        // 验证必需参数
        if (empty($shopId)) {
            sendError('店铺ID不能为空');
            return;
        }

        // 构建API请求参数
        $params = [
            'shop_id' => $shopId,
            'page' => $page,
            'size' => $size,
            'order_by' => 'create_time',
            'order_asc' => 'false'
        ];

        // 如果指定了达人ID，添加到参数中
        if (!empty($authorId)) {
            $params['author_id'] = $authorId;
        }

        // 可选的时间范围参数
        if (!empty($_POST['start_time'])) {
            $params['create_time_start'] = strtotime($_POST['start_time']);
        } else {
            // 默认查询最近30天
            $params['create_time_start'] = time() - (30 * 24 * 60 * 60);
        }

        if (!empty($_POST['end_time'])) {
            $params['create_time_end'] = strtotime($_POST['end_time']);
        } else {
            // 默认到当前时间
            $params['create_time_end'] = time();
        }

        // 默认查询所有状态的订单
        $params['combine_status'] = [[]];

        // 调用OrderService
        try {
            $result = $orderService->searchOrderList($params);
        } catch (Exception $e) {
            sendError('OrderService调用失败: ' . $e->getMessage() . ' 在文件 ' . $e->getFile() . ' 第 ' . $e->getLine() . ' 行');
            return;
        }

        if ($result['success']) {
            // 格式化返回数据
            $responseData = [
                'list' => $result['data']->list ?? [],
                'total' => $result['data']->total ?? 0,
                'has_more' => $result['data']->has_more ?? false,
                'author_stats' => $result['data']->author_stats ?? null,
                'debug_info' => $result['data']->debug_info ?? null,
                'pagination' => [
                    'current_page' => $page + 1,
                    'page_size' => $size,
                    'total' => $result['data']->total ?? 0,
                    'total_pages' => ceil(($result['data']->total ?? 0) / $size)
                ]
            ];

            sendSuccess($responseData, '获取订单列表成功');
        } else {
            // 包含调试信息
            $errorMessage = $result['message'] ?? '获取订单列表失败';
            if (isset($result['debug_info'])) {
                $errorMessage .= ' [调试信息: ' . json_encode($result['debug_info'], JSON_UNESCAPED_UNICODE) . ']';
            }

            // 临时输出完整的result用于调试
            error_log("OrderService result: " . json_encode($result, JSON_UNESCAPED_UNICODE));

            sendError($errorMessage);
        }

    } catch (Exception $e) {
        sendError('获取订单列表失败: ' . $e->getMessage());
    }
}

/**
 * 调用抖店订单搜索API
 */
function callDouyinOrderSearchAPI($accessToken, $params) {
    try {
        // 暂时返回模拟数据进行测试
        // TODO: 实现真实的抖店订单搜索API调用

        $mockData = (object)[
            'items' => [
                (object)[
                    'order_id' => '6920726434910359662',
                    'product_name' => '东欧复古电影游戏团购',
                    'order_amount' => 588, // 分为单位
                    'order_status' => 4, // 已完成
                    'create_time' => time() - 86400, // 1天前
                    'author_info' => (object)[
                        'author_id' => $params['author_id'],
                        'author_name' => '测试达人_' . substr($params['author_id'], -4)
                    ]
                ],
                (object)[
                    'order_id' => '6920726434910359663',
                    'product_name' => '游戏充值卡',
                    'order_amount' => 1000,
                    'order_status' => 2, // 已支付
                    'create_time' => time() - 172800, // 2天前
                    'author_info' => (object)[
                        'author_id' => $params['author_id'],
                        'author_name' => '测试达人_' . substr($params['author_id'], -4)
                    ]
                ]
            ],
            'total' => 2,
            'has_more' => false
        ];

        return [
            'success' => true,
            'data' => $mockData,
            'message' => '获取成功'
        ];

    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'API调用异常: ' . $e->getMessage()
        ];
    }
}