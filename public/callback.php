<?php
session_start();

// 引入配置和服务类
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../src/Services/TokenManager.php';

$config = require __DIR__ . '/../config/config.php';

// 设置时区
date_default_timezone_set($config['system']['timezone']);

$tokenManager = new TokenManager($config);

$error = null;
$success = false;

try {
    // 获取授权码
    $code = $_GET['code'] ?? '';
    $state = $_GET['state'] ?? '';
    $shopId = $_GET['shop_id'] ?? '';

    if (empty($code)) {
        throw new Exception('授权失败：未获取到授权码');
    }

    // 通过授权码获取token
    $tokenData = $tokenManager->createToken($code, $shopId);

    if ($tokenData) {
        $success = true;
        $_SESSION['auth_success'] = true;
        $_SESSION['shop_id'] = $tokenData['shop_id'];
    } else {
        throw new Exception('获取token失败');
    }

} catch (Exception $e) {
    $error = $e->getMessage();
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>授权结果 - 抖店售后审核退款系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .result-section {
            text-align: center;
            padding: 50px 0;
        }
        .result-section .card {
            max-width: 600px;
            margin: 0 auto;
        }
        .success-icon {
            color: #28a745;
            font-size: 4rem;
        }
        .error-icon {
            color: #dc3545;
            font-size: 4rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="bi bi-shop"></i> 抖店售后审核退款系统
            </a>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="result-section">
            <div class="card">
                <div class="card-body">
                    <?php if ($success): ?>
                        <i class="bi bi-check-circle-fill success-icon"></i>
                        <h4 class="card-title mt-3 text-success">授权成功</h4>
                        <p class="card-text text-muted">
                            恭喜！您已成功完成抖店开放平台授权，现在可以使用售后管理功能了。
                        </p>
                        <?php if (!empty($tokenData['shop_id'])): ?>
                            <p class="text-muted">
                                <small>店铺ID: <?php echo htmlspecialchars($tokenData['shop_id']); ?></small>
                            </p>
                        <?php endif; ?>
                        <div class="mt-4">
                            <a href="index.php" class="btn btn-primary btn-lg">
                                <i class="bi bi-arrow-right"></i> 进入系统
                            </a>
                        </div>
                    <?php else: ?>
                        <i class="bi bi-x-circle-fill error-icon"></i>
                        <h4 class="card-title mt-3 text-danger">授权失败</h4>
                        <p class="card-text text-muted">
                            很抱歉，授权过程中出现了错误，请重试。
                        </p>
                        <?php if ($error): ?>
                            <div class="alert alert-danger mt-3">
                                <strong>错误详情：</strong> <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>
                        <div class="mt-4">
                            <a href="index.php" class="btn btn-primary">
                                <i class="bi bi-arrow-left"></i> 返回首页
                            </a>
                            <a href="<?php echo $tokenManager->getAuthUrl(); ?>" class="btn btn-outline-primary ms-2">
                                <i class="bi bi-arrow-clockwise"></i> 重新授权
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <?php if ($success): ?>
    <script>
        // 3秒后自动跳转到首页
        setTimeout(function() {
            window.location.href = 'index.php';
        }, 3000);
    </script>
    <?php endif; ?>
</body>
</html>