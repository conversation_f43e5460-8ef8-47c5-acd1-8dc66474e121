<?php
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../src/Services/DebugLogger.php';

$config = require __DIR__ . '/../config/config.php';
$debugLogger = new DebugLogger($config);

// 获取调试统计信息
$stats = $debugLogger->getStats();

// 读取最新的调试日志
$debugLogFile = $config['system']['debug_log_file'] ?? __DIR__ . '/../logs/debug.log';
$appLogFile = $config['system']['log_file'] ?? __DIR__ . '/../logs/app.log';

$debugLogs = [];
$appLogs = [];

if (file_exists($debugLogFile)) {
    $lines = file($debugLogFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $debugLogs = array_slice(array_reverse($lines), 0, 50); // 最新50条
}

if (file_exists($appLogFile)) {
    $lines = file($appLogFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $appLogs = array_slice(array_reverse($lines), 0, 50); // 最新50条
}

// 检查token状态
$shops = array_keys($config['shops']);
$tokenStatus = [];

foreach ($shops as $shopId) {
    $tokenFile = __DIR__ . "/../storage/tokens_{$shopId}.json";
    if (file_exists($tokenFile)) {
        $tokenData = json_decode(file_get_contents($tokenFile), true);
        $currentTime = time();
        $expiresAt = $tokenData['expires_at'] ?? 0;
        $timeToExpire = $expiresAt - $currentTime;
        
        $tokenStatus[$shopId] = [
            'shop_name' => $config['shops'][$shopId]['name'],
            'expires_at' => date('Y-m-d H:i:s', $expiresAt),
            'time_to_expire' => $timeToExpire,
            'status' => $timeToExpire > 0 ? 'valid' : 'expired',
            'hours_remaining' => round($timeToExpire / 3600, 1)
        ];
    } else {
        $tokenStatus[$shopId] = [
            'shop_name' => $config['shops'][$shopId]['name'],
            'status' => 'missing'
        ];
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试面板 - 抖店售后管理系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .card h2 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        .stat-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #3498db;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        .token-status {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }
        .token-item {
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #27ae60;
        }
        .token-item.expired {
            border-left-color: #e74c3c;
            background: #fdf2f2;
        }
        .token-item.missing {
            border-left-color: #f39c12;
            background: #fef9e7;
        }
        .token-item.valid {
            background: #f0f9f0;
        }
        .log-container {
            max-height: 400px;
            overflow-y: auto;
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 4px;
            background: rgba(255,255,255,0.05);
        }
        .log-level-DEBUG { border-left: 3px solid #3498db; }
        .log-level-ERROR { border-left: 3px solid #e74c3c; }
        .log-level-API { border-left: 3px solid #9b59b6; }
        .log-level-PERFORMANCE { border-left: 3px solid #f39c12; }
        .refresh-btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }
        .refresh-btn:hover {
            background: #2980b9;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            background: #ecf0f1;
            border: none;
            cursor: pointer;
            border-radius: 6px 6px 0 0;
            margin-right: 5px;
        }
        .tab.active {
            background: #3498db;
            color: white;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 调试面板</h1>
        
        <!-- 系统统计 -->
        <div class="card">
            <h2>📊 系统统计</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-label">调试模式</div>
                    <div class="stat-value"><?= $stats['debug_mode'] ? '开启' : '关闭' ?></div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">内存使用</div>
                    <div class="stat-value"><?= $stats['memory_usage'] ?></div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">峰值内存</div>
                    <div class="stat-value"><?= $stats['peak_memory'] ?></div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">主日志大小</div>
                    <div class="stat-value"><?= $stats['log_file_size'] ?></div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">调试日志大小</div>
                    <div class="stat-value"><?= $stats['debug_log_size'] ?></div>
                </div>
                <div class="stat-item">
                    <div class="stat-label">执行时间</div>
                    <div class="stat-value"><?= $stats['execution_time'] ?></div>
                </div>
            </div>
        </div>

        <!-- Token状态 -->
        <div class="card">
            <h2>🔑 Token状态</h2>
            <div class="token-status">
                <?php foreach ($tokenStatus as $shopId => $status): ?>
                <div class="token-item <?= $status['status'] ?>">
                    <h4><?= htmlspecialchars($status['shop_name']) ?></h4>
                    <p><strong>店铺ID:</strong> <?= $shopId ?></p>
                    <?php if ($status['status'] === 'valid'): ?>
                        <p><strong>过期时间:</strong> <?= $status['expires_at'] ?></p>
                        <p><strong>剩余时间:</strong> <?= $status['hours_remaining'] ?> 小时</p>
                        <p style="color: #27ae60;">✅ 有效</p>
                    <?php elseif ($status['status'] === 'expired'): ?>
                        <p><strong>过期时间:</strong> <?= $status['expires_at'] ?></p>
                        <p style="color: #e74c3c;">❌ 已过期</p>
                    <?php else: ?>
                        <p style="color: #f39c12;">⚠️ Token文件缺失</p>
                    <?php endif; ?>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- 日志查看 -->
        <div class="card">
            <h2>📝 日志查看</h2>
            <button class="refresh-btn" onclick="location.reload()">🔄 刷新</button>
            
            <div class="tabs">
                <button class="tab active" onclick="showTab('debug')">调试日志</button>
                <button class="tab" onclick="showTab('app')">应用日志</button>
            </div>

            <div id="debug-tab" class="tab-content active">
                <div class="log-container">
                    <?php foreach ($debugLogs as $log): ?>
                        <?php 
                        $logData = json_decode($log, true);
                        if ($logData):
                            $level = $logData['level'] ?? 'INFO';
                        ?>
                        <div class="log-entry log-level-<?= $level ?>">
                            <strong>[<?= $logData['timestamp'] ?? 'N/A' ?>] <?= $level ?></strong><br>
                            <?= htmlspecialchars($logData['message'] ?? '') ?><br>
                            <?php if (!empty($logData['data'])): ?>
                                <small><?= htmlspecialchars(json_encode($logData['data'], JSON_UNESCAPED_UNICODE)) ?></small>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>

            <div id="app-tab" class="tab-content">
                <div class="log-container">
                    <?php foreach ($appLogs as $log): ?>
                        <?php 
                        $logData = json_decode($log, true);
                        if ($logData):
                        ?>
                        <div class="log-entry">
                            <strong>[<?= $logData['timestamp'] ?? 'N/A' ?>]</strong><br>
                            操作: <?= htmlspecialchars($logData['operation'] ?? 'N/A') ?><br>
                            状态: <?= ($logData['success'] ?? false) ? '✅ 成功' : '❌ 失败' ?><br>
                            <?php if (!empty($logData['error'])): ?>
                                <span style="color: #e74c3c;">错误: <?= htmlspecialchars($logData['error']) ?></span><br>
                            <?php endif; ?>
                        </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的active类
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName + '-tab').classList.add('active');
            
            // 添加active类到选中的标签
            event.target.classList.add('active');
        }
    </script>
</body>
</html>
