<?php
require_once __DIR__ . '/../config/config.php';
$config = require __DIR__ . '/../config/config.php';
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抖店售后管理系统</title>
    <style>
        /* Arco Design 风格 */
        * { box-sizing: border-box; }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
            background-color: #f7f8fa;
            margin: 0;
            padding: 0;
            font-size: 14px;
            line-height: 1.5715;
            color: #1d2129;
        }

        /* Arco 容器 */
        .arco-layout {
            min-height: 100vh;
        }

        .arco-layout-header {
            background: #fff;
            border-bottom: 1px solid #e5e6eb;
            padding: 0 24px;
            height: 64px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 5px 1px rgba(0, 0, 0, 0.06);
        }

        .arco-layout-content {
            padding: 24px;
            background: #f7f8fa;
            min-height: calc(100vh - 64px);
        }

        .arco-typography-title {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: #1d2129;
        }

        .arco-typography-text {
            color: #86909c;
            font-size: 14px;
        }

        /* Arco 卡片 */
        .arco-card {
            background: #fff;
            border-radius: 6px;
            border: 1px solid #e5e6eb;
            margin-bottom: 16px;
            overflow: hidden;
        }

        .arco-card-header {
            padding: 20px 24px 16px;
            border-bottom: 1px solid #e5e6eb;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .arco-card-title {
            font-size: 16px;
            font-weight: 600;
            color: #1d2129;
            margin: 0;
        }

        .arco-card-body {
            padding: 20px 24px;
        }

        /* Arco 表单 */
        .arco-form {
            display: flex;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
        }

        .arco-form-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .arco-form-item-label {
            color: #1d2129;
            font-size: 14px;
            font-weight: 500;
            white-space: nowrap;
        }

        .arco-select {
            min-width: 200px;
            height: 32px;
            padding: 0 12px;
            border: 1px solid #c9cdd4;
            border-radius: 6px;
            background: #fff;
            font-size: 14px;
            color: #1d2129;
            transition: all 0.1s cubic-bezier(0, 0, 1, 1);
        }

        .arco-select:hover {
            border-color: #165dff;
        }

        .arco-select:focus {
            outline: none;
            border-color: #165dff;
            box-shadow: 0 0 0 2px rgba(22, 93, 255, 0.1);
        }

        .arco-btn {
            height: 32px;
            padding: 0 15px;
            border: 1px solid #165dff;
            background: #165dff;
            color: #fff;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 400;
            transition: all 0.1s cubic-bezier(0, 0, 1, 1);
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .arco-btn:hover {
            background: #4080ff;
            border-color: #4080ff;
        }

        .arco-btn:active {
            background: #0e42d2;
            border-color: #0e42d2;
        }

        .arco-statistic {
            background: #f2f3f5;
            padding: 12px 16px;
            border-radius: 6px;
            border: 1px solid #e5e6eb;
            min-width: 200px;
        }

        .arco-statistic-title {
            color: #86909c;
            font-size: 12px;
            margin-bottom: 4px;
        }

        .arco-statistic-content {
            color: #1d2129;
            font-size: 14px;
            font-weight: 500;
        }

        /* Arco 表格 */
        .arco-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            font-size: 14px;
        }

        .arco-table th {
            background: #f7f8fa;
            color: #1d2129;
            font-weight: 500;
            padding: 16px 12px;
            text-align: left;
            border-bottom: 1px solid #e5e6eb;
            font-size: 14px;
        }

        .arco-table td {
            padding: 16px 12px;
            border-bottom: 1px solid #e5e6eb;
            color: #1d2129;
            vertical-align: middle;
        }

        .arco-table tbody tr:hover {
            background: #f7f8fa;
        }

        .arco-table tbody tr:last-child td {
            border-bottom: none;
        }

        /* Arco 标签 */
        .arco-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 400;
            line-height: 20px;
            white-space: nowrap;
        }

        .arco-tag-green {
            background: #e8f5e8;
            color: #00b42a;
            border: 1px solid #7bc67e;
        }

        .arco-tag-blue {
            background: #e8f3ff;
            color: #165dff;
            border: 1px solid #7bc7ff;
        }

        .arco-tag-orange {
            background: #fff7e8;
            color: #ff7d00;
            border: 1px solid #ffb65d;
        }

        .arco-tag-red {
            background: #ffece8;
            color: #f53f3f;
            border: 1px solid #ff9a8e;
        }

        .arco-tag-gray {
            background: #f2f3f5;
            color: #86909c;
            border: 1px solid #c9cdd4;
        }

        /* Arco 加载 */
        .arco-spin {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #e5e6eb;
            border-top: 2px solid #165dff;
            border-radius: 50%;
            animation: arco-spin 1s linear infinite;
        }

        @keyframes arco-spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .arco-empty {
            text-align: center;
            padding: 40px 20px;
            color: #86909c;
        }

        .arco-empty-icon {
            font-size: 48px;
            margin-bottom: 16px;
            opacity: 0.6;
        }

        .arco-empty-description {
            font-size: 14px;
            color: #86909c;
        }

        /* 响应式 */
        @media (max-width: 768px) {
            .arco-layout-content {
                padding: 16px;
            }

            .arco-card-header,
            .arco-card-body {
                padding: 16px;
            }

            .arco-form {
                flex-direction: column;
                align-items: stretch;
                gap: 12px;
            }

            .arco-form-item {
                flex-direction: column;
                align-items: stretch;
                gap: 4px;
            }

            .arco-select {
                min-width: auto;
            }

            .arco-table {
                font-size: 12px;
            }

            .arco-table th,
            .arco-table td {
                padding: 8px 6px;
            }
        }

        /* 数据表格容器 */
        .table-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .table-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem;
            font-weight: 600;
            font-size: 1.1rem;
        }

        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.875rem;
        }

        .data-table th {
            background: #f8fafc;
            padding: 1rem 0.75rem;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            font-size: 0.8rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .data-table td {
            padding: 1rem 0.75rem;
            border-bottom: 1px solid #f1f5f9;
            vertical-align: middle;
        }

        .data-table tbody tr {
            transition: all 0.2s ease;
        }

        .data-table tbody tr:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: scale(1.001);
        }

        /* 状态徽章 */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.375rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-closed { background: #dcfce7; color: #166534; }
        .status-processing { background: #fef3c7; color: #92400e; }
        .status-pending { background: #dbeafe; color: #1e40af; }
        .status-rejected { background: #fecaca; color: #991b1b; }

        .type-badge {
            background: #e0e7ff;
            color: #3730a3;
            padding: 0.25rem 0.5rem;
            border-radius: 8px;
            font-size: 0.7rem;
            font-weight: 500;
        }

        /* 金额显示 */
        .amount {
            font-weight: 700;
            color: #059669;
            font-family: 'SF Mono', Monaco, monospace;
        }

        /* 加载状态 */
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 3rem;
            flex-direction: column;
            gap: 1rem;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #e5e7eb;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: #6b7280;
            font-weight: 500;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 3rem;
            color: #6b7280;
        }

        .empty-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        /* 分页 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 1rem;
            padding: 1.5rem;
            background: #f8fafc;
        }

        .pagination-btn {
            padding: 0.5rem 1rem;
            border: 1px solid #d1d5db;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 0.875rem;
        }

        .pagination-btn:hover:not(:disabled) {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .pagination-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .pagination-info {
            color: #6b7280;
            font-size: 0.875rem;
        }

        /* 响应式设计 */
        @media (max-width: 1024px) {
            .control-row {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .stats-display {
                min-width: auto;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 0 1rem;
                margin: 1rem auto;
            }

            .header-content {
                padding: 0 1rem;
            }

            .control-panel {
                padding: 1rem;
            }

            .data-table {
                font-size: 0.75rem;
            }

            .data-table th,
            .data-table td {
                padding: 0.5rem 0.375rem;
            }
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
    <style>
        .status-badge {
            font-size: 0.875rem;
        }
        .operation-buttons .btn {
            margin-right: 0.25rem;
        }
        .table th {
            background-color: #f8f9fa;
            border-top: none;
        }
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="arco-layout">
        <!-- 顶部导航 -->
        <div class="arco-layout-header">
            <h1 class="arco-typography-title">抖店售后管理系统</h1>
            <span class="arco-typography-text" id="currentTime"></span>
        </div>

        <!-- 主内容区 -->
        <div class="arco-layout-content">
            <!-- 控制面板 -->
            <div class="arco-card">
                <div class="arco-card-header">
                    <h3 class="arco-card-title">店铺管理</h3>
                </div>
                <div class="arco-card-body">
                    <div class="arco-form">
                        <div class="arco-form-item">
                            <label class="arco-form-item-label">选择店铺</label>
                            <select id="shopSelector" class="arco-select" onchange="switchShop()">
                                <option value="">请选择店铺</option>
                                <option value="228120078">短云游戏旗舰店</option>
                                <option value="203296334">三秒权益</option>
                                <option value="231044430">三秒数字权益</option>
                            </select>
                        </div>
                        <div class="arco-statistic" id="shopStats">
                            <div class="arco-statistic-title">统计信息</div>
                            <div class="arco-statistic-content">请选择店铺</div>
                        </div>
                        <button class="arco-btn" onclick="loadAftersaleList()">
                            <span id="loadingSpinner" class="arco-spin" style="display: none;"></span>
                            刷新数据
                        </button>
                    </div>
                </div>
            </div>

            <!-- 数据表格 -->
            <div class="arco-card">
                <div class="arco-card-header">
                    <h3 class="arco-card-title">售后订单列表</h3>
                    <div style="display: flex; gap: 12px; align-items: center;">
                        <select class="arco-select" style="min-width: 120px;" id="statusFilter" onchange="loadAftersaleList(1)">
                            <option value="6" selected>待商家处理(状态6)</option>
                        </select>
                        <input type="text" class="arco-select" style="min-width: 200px;" id="searchInput" placeholder="搜索订单号/售后单号" onkeypress="handleSearchKeyPress(event)">
                        <button class="arco-btn" onclick="searchOrders()">搜索</button>
                        <button class="arco-btn" onclick="clearSearch()" style="background: #f2f3f5; color: #86909c; border-color: #c9cdd4;">清空</button>
                    </div>
                </div>
                <div class="arco-card-body">
                    <div id="aftersaleList">
                        <div class="arco-empty">
                            <div class="arco-empty-icon">📦</div>
                            <div class="arco-empty-description">正在加载数据...</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
                            <!-- 售后列表将在这里显示 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 移除了Bootstrap模态框，使用简单的alert替代 -->

    <!-- 移除外部JS依赖，使用原生JavaScript -->
    <script>
        // 更新时间显示
        function updateTime() {
            const now = new Date();
            document.getElementById('currentTime').textContent = now.toLocaleString('zh-CN');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateTime();
            setInterval(updateTime, 1000);

            // 设置默认店铺并加载数据
            const shopSelector = document.getElementById('shopSelector');
            if (shopSelector.options.length > 1) {
                shopSelector.value = '228120078'; // 默认选择短云游戏旗舰店
                switchShop(); // 触发店铺切换，会自动加载数据
            }
        });

        // 店铺切换功能
        function switchShop() {
            const shopSelector = document.getElementById('shopSelector');
            const selectedShopId = shopSelector.value;
            const selectedShopName = shopSelector.options[shopSelector.selectedIndex].text;

            // 更新统计信息显示
            document.getElementById('shopStats').textContent = '正在切换店铺，加载中...';

            showAlert('info', `正在切换到店铺: ${selectedShopName}`);
            loadAftersaleList(1); // 重新加载第一页
        }

        // 加载售后列表
        function loadAftersaleList(page = 1) {
            const selectedShopId = document.getElementById('shopSelector').value;
            const statusFilter = document.getElementById('statusFilter').value;
            console.log('当前选择的店铺ID:', selectedShopId);

            // 如果没有选择店铺，提示选择店铺
            if (!selectedShopId) {
                document.getElementById('aftersaleList').innerHTML = `
                    <div class="arco-empty">
                        <div class="arco-empty-icon">🏪</div>
                        <div class="arco-empty-description">请先选择店铺</div>
                    </div>
                `;
                return;
            }

            document.getElementById('loadingSpinner').style.display = 'inline-block';
            document.getElementById('aftersaleList').innerHTML = `
                <div class="arco-empty">
                    <div class="arco-spin" style="margin: 0 auto 16px;"></div>
                    <div class="arco-empty-description">正在加载数据...</div>
                </div>
            `;

            // 添加搜索条件
            const searchInput = document.getElementById('searchInput').value.trim();

            // 重置重试标记
            window.hasRetried = false;

            const formData = new FormData();
            formData.append('page', page - 1); // API使用0基索引
            formData.append('size', statusFilter ? 100 : 20); // 筛选时获取更多数据
            formData.append('shop_id', selectedShopId);

            // 使用状态筛选器的值
            if (statusFilter) {
                formData.append('standard_aftersale_status', JSON.stringify([parseInt(statusFilter)]));
                formData.append('need_special_type', 'true');
                console.log('🎯 使用状态筛选器:', statusFilter, '- 时间戳:', new Date().toLocaleTimeString());
            } else {
                console.log('⚠️ 没有状态筛选器，显示所有数据');
            }
            window.currentStatusFilter = null; // 不需要前端筛选

            if (searchInput) {
                // 判断是订单号还是售后单号
                if (searchInput.length > 15) {
                    formData.append('order_id', searchInput);
                } else {
                    formData.append('aftersale_id', searchInput);
                }
            }

            // 显示FormData的所有内容
            const formDataEntries = {};
            for (let [key, value] of formData.entries()) {
                formDataEntries[key] = value;
            }
            console.log('实际发送的FormData:', formDataEntries);
            console.log('特别检查 standard_aftersale_status:', formDataEntries.standard_aftersale_status);
            console.log('特别检查 need_special_type:', formDataEntries.need_special_type);

            fetch('api.php?action=list', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                document.getElementById('loadingSpinner').style.display = 'none';
                console.log('API响应:', data);
                console.log('data.data:', data.data);
                console.log('data.data.list:', data.data ? data.data.list : 'undefined');
                console.log('data.data.list.length:', data.data && data.data.list ? data.data.list.length : 'undefined');

                // 显示API调试信息
                if (data.data && data.data.debug) {
                    console.log('🔍 API调试信息:', data.data.debug);
                }

                if (data.success) {
                    renderAftersaleList(data.data);
                } else {
                    showError('加载失败: ' + (data.message || '未知错误'));
                }
            })
            .catch(error => {
                document.getElementById('loadingSpinner').style.display = 'none';
                showError('网络错误: ' + error.message);
            });
        }

        // 渲染售后列表
        function renderAftersaleList(data) {
            try {
                console.log('renderAftersaleList 接收到的数据:', data);
                console.log('data.list 是否存在:', !!data.list);
                console.log('data.list 长度:', data.list ? data.list.length : 'undefined');

                // 禁用回退逻辑 - 我们只显示状态6的订单
                // 如果没有状态6的订单，就显示空列表
                console.log('🎯 强制只显示状态6订单，不使用回退逻辑');

                // 更新店铺统计信息
                const shopSelector = document.getElementById('shopSelector');
                const selectedShopName = shopSelector.options[shopSelector.selectedIndex].text;
                const totalCount = data.total || 0;
                const currentPageCount = data.list ? data.list.length : 0;

                document.getElementById('shopStats').innerHTML = `
                    <div class="arco-statistic-title">当前店铺: ${selectedShopName}</div>
                    <div class="arco-statistic-content">总数: ${totalCount} | 显示: ${currentPageCount}</div>
                `;

                if (!data || !data.list || data.list.length === 0) {
                    console.log('数据为空，显示暂无订单提示');
                    document.getElementById('aftersaleList').innerHTML = `
                        <div class="arco-empty">
                            <div class="arco-empty-icon">📦</div>
                            <div class="arco-empty-description">暂无售后订单数据</div>
                        </div>
                    `;
                    return;
                }

                console.log('开始渲染售后列表，数据:', data);

                let html = `
                    <table class="arco-table">
                        <thead>
                            <tr>
                                <th>售后单号</th>
                                <th>订单号</th>
                                <th>商品名称</th>
                                <th>售后类型</th>
                                <th>售后状态</th>
                                <th>退款金额</th>
                                <th>申请时间</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

                if (data.list.length === 0) {
                    console.log('没有找到状态6的订单');
                    html += `
                        <tr>
                            <td colspan="8" style="text-align: center; padding: 40px; color: #86909c;">
                                <div style="font-size: 16px; margin-bottom: 8px;">📋 暂无数据</div>
                                <div style="font-size: 14px;">当前没有状态为6的待商家处理订单</div>
                            </td>
                        </tr>
                    `;
                } else {
                    console.log(`找到 ${data.list.length} 条状态6的订单`);
                }

                data.list.forEach((item, index) => {
                    try {
                        const aftersaleInfo = item.aftersale_info || {};
                        const orderInfo = item.order_info || {};
                        const relatedOrderInfo = orderInfo.related_order_info || [];
                        const firstOrderInfo = relatedOrderInfo.length > 0 ? relatedOrderInfo[0] : {};

                        const aftersaleId = aftersaleInfo.aftersale_id || 'N/A';
                        const orderId = orderInfo.shop_order_id || 'N/A';
                        const productName = firstOrderInfo.product_name || '未知商品';
                        const aftersaleType = aftersaleInfo.aftersale_type || 0;
                        const aftersaleStatus = aftersaleInfo.aftersale_status || 0;

                        // 前端状态过滤
                        if (window.currentStatusFilter && window.currentStatusFilter.length > 0) {
                            if (!window.currentStatusFilter.includes(aftersaleStatus.toString())) {
                                return; // 跳过不匹配的状态
                            }
                        }
                        const refundAmount = aftersaleInfo.refund_amount || 0;
                        const createTime = aftersaleInfo.create_time || aftersaleInfo.apply_time || 0;

                        const displayProductName = productName.length > 30 ? productName.substring(0, 30) + '...' : productName;
                        const typeText = getAftersaleTypeText(aftersaleType);
                        const statusText = getAftersaleStatusText(aftersaleStatus);
                        const statusColor = getStatusColor(aftersaleStatus);
                        const formattedAmount = (refundAmount / 100).toFixed(2);
                        const formattedTime = formatDateTime(createTime);
                        const operationButtons = getOperationButtons(aftersaleInfo);

                        const statusTagClass = getArcoStatusClass(aftersaleStatus);

                        html += '<tr>';
                        html += '<td><strong>' + aftersaleId + '</strong></td>';
                        html += '<td>' + orderId + '</td>';
                        html += '<td title="' + productName + '">' + displayProductName + '</td>';
                        html += '<td><span class="arco-tag arco-tag-blue">' + typeText + '</span></td>';
                        html += '<td><span class="arco-tag ' + statusTagClass + '">' + statusText + '</span></td>';
                        html += '<td><strong style="color: #00b42a;">¥' + formattedAmount + '</strong></td>';
                        html += '<td style="font-size: 12px; color: #86909c;">' + formattedTime + '</td>';
                        html += '<td>' + getOperationButtons(aftersaleInfo) + '</td>';
                        html += '</tr>';
                    } catch (error) {
                        console.error('Error processing item', index, ':', error, item);
                        html += '<tr><td colspan="8">数据处理错误: ' + error.message + '</td></tr>';
                    }
                });

                html += `
                        </tbody>
                    </table>
                `;

                // 添加分页 - 当有多页数据时显示
                if (data.pagination && data.pagination.total_pages > 1) {
                    html += renderPagination(data.pagination);
                }

                console.log('渲染完成，最终HTML长度:', html.length);
                document.getElementById('aftersaleList').innerHTML = html;
                console.log('HTML已插入到DOM中');
            } catch (error) {
                console.error('渲染售后列表时出错:', error);
                document.getElementById('aftersaleList').innerHTML = `
                    <div class="alert alert-danger">
                        <h5>渲染错误</h5>
                        <p>渲染售后列表时出现错误: ${error.message}</p>
                        <p>请刷新页面重试，或联系技术支持。</p>
                    </div>
                `;
            }
        }

        // 获取操作按钮
        function getOperationButtons(aftersaleInfo) {
            const status = aftersaleInfo.aftersale_status;

            // 状态6和11是待商家处理
            if (status == 6 || status == 11) {
                return `
                    <div style="display: flex; gap: 4px;">
                        <button class="arco-btn" style="height: 24px; padding: 0 8px; font-size: 12px; background: #00b42a; color: white; border-color: #00b42a;"
                                onclick="handleAftersale('${aftersaleInfo.aftersale_id}', 201, '同意退款')">
                            同意
                        </button>
                        <button class="arco-btn" style="height: 24px; padding: 0 8px; font-size: 12px; background: #f53f3f; color: white; border-color: #f53f3f;"
                                onclick="handleAftersale('${aftersaleInfo.aftersale_id}', 202, '拒绝退款')">
                            拒绝
                        </button>
                    </div>
                `;
            }

            return `<span style="color: #86909c; font-size: 12px;">无可用操作</span>`;
        }

        // 处理售后操作
        function handleAftersale(aftersaleId, operationType, operationText) {
            const formData = new FormData();
            formData.append('aftersale_id', aftersaleId);
            formData.append('operation_type', operationType);

            // 如果是同意退款，直接同意，不需要输入金额
            if (operationType == 201) {
                console.log('直接同意退款，售后单号:', aftersaleId);
            }

            // 如果是拒绝退款，需要拒绝原因
            if (operationType == 202) {
                const reason = prompt('请输入拒绝原因：');
                if (reason === null) return; // 用户取消
                if (reason && reason.trim()) {
                    formData.append('reject_reason', reason.trim());
                } else {
                    alert('请输入拒绝原因');
                    return;
                }
            }

            fetch('api.php?action=operate', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('success', `${operationText}成功`);
                    loadAftersaleList(); // 重新加载列表
                } else {
                    showAlert('error', data.message || `${operationText}失败`);
                }
            })
            .catch(error => {
                showAlert('error', '网络错误: ' + error.message);
            });
        }

        // 暂时移除复杂的多店铺功能

        // 暂时移除复杂的显示函数

        // 暂时移除多店铺状态筛选功能

        // 暂时移除状态筛选文本函数

        // 搜索功能 - 支持多店铺联动
        function searchOrders() {
            const searchInput = document.getElementById('searchInput').value.trim();

            // 如果有搜索内容，进行多店铺搜索
            if (searchInput) {
                searchAcrossAllShops(searchInput);
            } else {
                loadAftersaleList(1); // 没有搜索内容时正常加载
            }
        }

        // 清空搜索
        function clearSearch() {
            document.getElementById('statusFilter').value = '';
            document.getElementById('searchInput').value = '';
            loadAftersaleList(1); // 重新加载第一页
        }

        // 处理搜索框回车事件
        function handleSearchKeyPress(event) {
            if (event.key === 'Enter') {
                searchOrders();
            }
        }

        // 多店铺联动搜索
        async function searchAcrossAllShops(searchTerm) {
            const shopSelector = document.getElementById('shopSelector');
            const allShops = Array.from(shopSelector.options).map(option => ({
                id: option.value,
                name: option.text
            }));

            // 显示搜索状态
            document.getElementById('aftersaleList').innerHTML = `
                <div class="arco-empty">
                    <div class="arco-spin" style="margin: 0 auto 16px;"></div>
                    <div class="arco-empty-description">正在搜索所有店铺...</div>
                </div>
            `;

            let foundResults = [];
            let foundShop = null;

            // 遍历所有店铺进行搜索
            for (const shop of allShops) {
                if (!shop.id) continue; // 跳过空选项

                try {
                    const result = await searchInShop(shop.id, searchTerm);
                    if (result && result.list && result.list.length > 0) {
                        foundResults = result.list;
                        foundShop = shop;
                        break; // 找到结果就停止搜索
                    }
                } catch (error) {
                    console.log(`搜索店铺 ${shop.name} 时出错:`, error);
                }
            }

            if (foundResults.length > 0) {
                // 自动切换到找到结果的店铺
                shopSelector.value = foundShop.id;

                // 显示搜索结果
                displaySearchResults(foundResults, foundShop.name, searchTerm);

                showAlert('success', `在 ${foundShop.name} 中找到 ${foundResults.length} 条结果`);
            } else {
                // 没有找到结果
                document.getElementById('aftersaleList').innerHTML = `
                    <div class="arco-empty">
                        <div class="arco-empty-icon">🔍</div>
                        <div class="arco-empty-description">在所有店铺中都未找到 "${searchTerm}" 的相关记录</div>
                    </div>
                `;
                showAlert('info', '未找到相关记录');
            }
        }

        // 在指定店铺中搜索
        function searchInShop(shopId, searchTerm) {
            const formData = new FormData();
            formData.append('page', 1);
            formData.append('size', 20);
            formData.append('shop_id', shopId);

            // 判断是订单号还是售后单号
            if (searchTerm.length > 15) {
                formData.append('order_id', searchTerm);
            } else {
                formData.append('aftersale_id', searchTerm);
            }

            return fetch('api.php?action=list', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    return data.data;
                } else {
                    throw new Error(data.message || '搜索失败');
                }
            });
        }

        // 显示搜索结果
        function displaySearchResults(results, shopName, searchTerm) {
            let html = `
                <div style="margin-bottom: 16px; padding: 12px; background: #f2f3f5; border-radius: 6px;">
                    <strong>搜索结果</strong>：在 <span style="color: #165dff;">${shopName}</span> 中找到 "${searchTerm}" 的相关记录
                </div>
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background: #f2f3f5;">
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e6eb;">售后单号</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e6eb;">订单号</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e6eb;">商品名称</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e6eb;">售后类型</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e6eb;">售后状态</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e6eb;">退款金额</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e6eb;">申请时间</th>
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #e5e6eb;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            results.forEach(item => {
                const aftersaleInfo = item.aftersale_info || {};
                const orderInfo = item.order_info || {};
                const productInfo = item.product_info || {};

                const aftersaleId = aftersaleInfo.aftersale_id || '';
                const orderId = orderInfo.shop_order_id || '';
                const productName = productInfo.product_name || '未知商品';
                const aftersaleType = aftersaleInfo.aftersale_type || 0;
                const aftersaleStatus = aftersaleInfo.aftersale_status || 0;
                const refundAmount = aftersaleInfo.refund_amount || 0;
                const createTime = aftersaleInfo.create_time || aftersaleInfo.apply_time || 0;

                const displayProductName = productName.length > 30 ? productName.substring(0, 30) + '...' : productName;
                const typeText = getAftersaleTypeText(aftersaleType);
                const statusText = getAftersaleStatusText(aftersaleStatus);
                const statusColor = getStatusColor(aftersaleStatus);
                const formattedAmount = (refundAmount / 100).toFixed(2);
                const formattedTime = formatDateTime(createTime);
                const operationButtons = getOperationButtons(aftersaleInfo);

                html += `
                    <tr style="border-bottom: 1px solid #f2f3f5;">
                        <td style="padding: 12px;">${aftersaleId}</td>
                        <td style="padding: 12px;">${orderId}</td>
                        <td style="padding: 12px;" title="${productName}">${displayProductName}</td>
                        <td style="padding: 12px;">${typeText}</td>
                        <td style="padding: 12px;">
                            <span class="arco-tag ${getArcoStatusClass(aftersaleStatus)}" style="color: ${statusColor};">
                                ${statusText}
                            </span>
                        </td>
                        <td style="padding: 12px; color: #f53f3f;">¥${formattedAmount}</td>
                        <td style="padding: 12px;">${formattedTime}</td>
                        <td style="padding: 12px;">${operationButtons}</td>
                    </tr>
                `;
            });

            html += `
                    </tbody>
                </table>
            `;

            document.getElementById('aftersaleList').innerHTML = html;
        }

        // 辅助函数
        function getAftersaleStatusText(status) {
            const statusMap = {
                6: '待商家同意',
                7: '待买家退货',
                8: '待商家发货',
                11: '待商家二次同意',
                12: '售后成功',
                13: '换货/补寄/维修待买家收货',
                14: '换货/补寄/维修成功',
                27: '商家一次拒绝',
                28: '售后失败',
                29: '商家二次拒绝'
            };
            return statusMap[status] || `未知状态(${status})`;
        }

        function getAftersaleTypeText(type) {
            const typeMap = {
                1: '仅退款',
                2: '退货退款',
                3: '换货',
                4: '维修'
            };
            return typeMap[type] || '未知类型';
        }

        function getArcoStatusClass(status) {
            switch(status) {
                case 28: return 'arco-tag-green';  // 售后关闭
                case 1: case 2: case 3: return 'arco-tag-orange';  // 处理中
                case 6: case 12: return 'arco-tag-blue';  // 退款相关
                default: return 'arco-tag-gray';
            }
        }

        function getStatusColor(status) {
            const colorMap = {
                1: 'warning',
                2: 'info',
                3: 'danger',
                4: 'warning',
                5: 'info',
                6: 'success',
                7: 'danger',
                8: 'secondary',
                9: 'warning',
                10: 'info',
                11: 'info',
                12: 'success',
                13: 'secondary',
                14: 'warning',
                15: 'info',
                16: 'info',
                17: 'secondary',
                18: 'secondary',
                19: 'warning',
                20: 'success',
                21: 'danger',
                22: 'info',
                23: 'info',
                24: 'success',
                25: 'success',
                26: 'danger',
                27: 'secondary',
                28: 'success',
                29: 'success',
                30: 'success'
            };
            return colorMap[status] || 'secondary';
        }

        function formatDateTime(timestamp) {
            return new Date(timestamp * 1000).toLocaleString('zh-CN');
        }

        function renderPagination(pagination) {
            return `
                <div style="padding: 16px; border-top: 1px solid #e5e6eb; display: flex; justify-content: space-between; align-items: center;">
                    <div style="color: #86909c; font-size: 14px;">
                        共 ${pagination.total.toLocaleString()} 条记录，第 ${pagination.current_page} / ${pagination.total_pages} 页
                    </div>
                    <div style="display: flex; gap: 8px;">
                        <button class="arco-btn" onclick="loadAftersaleList(${pagination.current_page - 1})"
                                ${pagination.current_page <= 1 ? 'disabled style="opacity: 0.5; cursor: not-allowed;"' : ''}>
                            上一页
                        </button>
                        <button class="arco-btn" onclick="loadAftersaleList(${pagination.current_page + 1})"
                                ${pagination.current_page >= pagination.total_pages ? 'disabled style="opacity: 0.5; cursor: not-allowed;"' : ''}>
                            下一页
                        </button>
                    </div>
                </div>
            `;
        }

        function showError(message) {
            document.getElementById('aftersaleList').innerHTML = `
                <div class="alert alert-danger" role="alert">
                    <i class="bi bi-exclamation-triangle"></i>
                    ${message}
                </div>
            `;
        }

        function showAlert(type, message) {
            // 简化实现：使用console.log和必要时使用alert
            console.log(`[${type.toUpperCase()}] ${message}`);

            // 只对错误使用原生alert，避免打断用户操作
            if (type === 'error') {
                alert(`错误: ${message}`);
            }
        }
    </script>
</body>
</html>
