<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单搜索 - 抖店管理系统</title>
    <style>
        /* Arco Design 风格 */
        * { box-sizing: border-box; }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f7f8fa;
            color: #1d2129;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .arco-card {
            background: white;
            border-radius: 6px;
            box-shadow: 0 2px 5px 0 rgba(0, 0, 0, 0.06);
            margin-bottom: 16px;
        }
        
        .arco-card-header {
            padding: 20px 24px 0;
            border-bottom: 1px solid #e5e6eb;
        }
        
        .arco-card-title {
            font-size: 16px;
            font-weight: 500;
            margin: 0 0 16px 0;
        }
        
        .arco-card-body {
            padding: 20px 24px;
        }
        
        .arco-form {
            display: flex;
            flex-wrap: wrap;
            gap: 16px;
            align-items: end;
        }
        
        .arco-form-item {
            display: flex;
            flex-direction: column;
            min-width: 200px;
        }
        
        .arco-form-item-label {
            font-size: 14px;
            color: #1d2129;
            margin-bottom: 4px;
        }
        
        .arco-input, .arco-select {
            height: 32px;
            padding: 0 12px;
            border: 1px solid #c9cdd4;
            border-radius: 6px;
            font-size: 14px;
            outline: none;
            transition: border-color 0.2s;
        }
        
        .arco-input:focus, .arco-select:focus {
            border-color: #165dff;
        }
        
        .arco-btn {
            height: 32px;
            padding: 0 16px;
            border: 1px solid #165dff;
            border-radius: 6px;
            background: #165dff;
            color: white;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .arco-btn:hover {
            background: #4080ff;
            border-color: #4080ff;
        }
        
        .arco-btn-secondary {
            background: #f2f3f5;
            color: #86909c;
            border-color: #c9cdd4;
        }
        
        .arco-btn-secondary:hover {
            background: #e5e6eb;
        }
        
        .order-cards {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
            gap: 12px;
            margin-top: 16px;
        }

        .order-card {
            background: white;
            border: 1px solid #e5e6eb;
            border-radius: 6px;
            padding: 12px;
            transition: all 0.2s;
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }

        .order-card:hover {
            border-color: #165dff;
            box-shadow: 0 2px 8px rgba(22, 93, 255, 0.1);
        }

        .order-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .order-id {
            font-size: 14px;
            font-weight: 600;
            color: #1d2129;
        }

        .order-amount {
            font-size: 16px;
            font-weight: 600;
            color: #00b42a;
        }

        .order-content {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 12px;
        }

        .order-left {
            flex: 1;
            min-width: 0;
        }

        .order-right {
            flex-shrink: 0;
            text-align: right;
        }

        .product-name {
            font-size: 13px;
            color: #1d2129;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .order-time {
            font-size: 11px;
            color: #86909c;
        }

        .author-info {
            font-size: 11px;
            color: #165dff;
            line-height: 1.4;
        }

        .stats-panel {
            background: #f7f8fa;
            border: 1px solid #e5e6eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            display: none;
        }

        .stats-header {
            font-size: 16px;
            font-weight: 600;
            color: #1d2129;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
        }

        .stats-item {
            background: white;
            padding: 16px;
            border-radius: 6px;
            border: 1px solid #e5e6eb;
        }

        .stats-label {
            font-size: 12px;
            color: #86909c;
            margin-bottom: 4px;
        }

        .stats-value {
            font-size: 20px;
            font-weight: 600;
            color: #1d2129;
        }

        .stats-value.success { color: #00b42a; }
        .stats-value.warning { color: #ff7d00; }
        .stats-value.danger { color: #f53f3f; }
        .stats-value.info { color: #165dff; }

        .status-breakdown {
            margin-top: 16px;
        }

        .status-breakdown-title {
            font-size: 14px;
            font-weight: 500;
            color: #1d2129;
            margin-bottom: 8px;
        }

        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            font-size: 12px;
        }

        .status-name {
            color: #4e5969;
        }

        .status-count {
            font-weight: 500;
            color: #1d2129;
        }

        .pagination-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 24px;
            padding: 16px 0;
            border-top: 1px solid #e5e6eb;
        }

        .pagination-info {
            font-size: 14px;
            color: #86909c;
        }

        .pagination {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .pagination-btn {
            min-width: 32px;
            height: 32px;
            padding: 0 8px;
            border: 1px solid #d0d0d0;
            border-radius: 4px;
            background: white;
            color: #1d2129;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .pagination-btn:hover:not(:disabled):not(.active) {
            border-color: #165dff;
            color: #165dff;
        }

        .pagination-btn:disabled {
            background: #f7f8fa;
            color: #c9cdd4;
            cursor: not-allowed;
            border-color: #e5e6eb;
        }

        .pagination-btn.active {
            background: #165dff;
            color: white;
            border-color: #165dff;
        }

        .pagination-ellipsis {
            padding: 0 4px;
            color: #86909c;
            font-size: 14px;
        }

        .pagination-size-selector {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #86909c;
        }

        .pagination-size-select {
            padding: 4px 8px;
            border: 1px solid #d0d0d0;
            border-radius: 4px;
            background: white;
            font-size: 14px;
            cursor: pointer;
        }

        .pagination-jump {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-left: 16px;
        }

        .pagination-jump-input {
            width: 50px;
            height: 32px;
            padding: 0 8px;
            border: 1px solid #d0d0d0;
            border-radius: 4px;
            text-align: center;
            font-size: 14px;
        }

        .pagination-jump-btn {
            height: 32px;
            padding: 0 12px;
            border: 1px solid #d0d0d0;
            border-radius: 4px;
            background: white;
            color: #1d2129;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .pagination-jump-btn:hover {
            border-color: #165dff;
            color: #165dff;
        }

        .order-status {
            display: inline-block;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 11px;
            font-weight: 500;
            margin-bottom: 4px;
        }

        .status-completed { background: #e8ffea; color: #00b42a; }
        .status-shipped { background: #e8f3ff; color: #165dff; }
        .status-paid { background: #fff7e8; color: #ff7d00; }
        .status-pending { background: #f2f3f5; color: #86909c; }
        .status-closed { background: #ffece8; color: #f53f3f; }
        .status-cancelled { background: #ffece8; color: #f53f3f; }
        .status-using { background: #e8f3ff; color: #165dff; }
        .status-default { background: #f2f3f5; color: #86909c; }

        .product-image {
            width: 40px;
            height: 40px;
            border-radius: 4px;
            object-fit: cover;
            margin-right: 8px;
            flex-shrink: 0;
        }

        .product-info {
            display: flex;
            align-items: center;
            margin-bottom: 4px;
        }

        .product-details {
            flex: 1;
            min-width: 0;
        }
        
        .arco-empty {
            text-align: center;
            padding: 40px;
            color: #86909c;
        }
        
        .arco-spin {
            display: inline-block;
            width: 16px;
            height: 16px;
            border: 2px solid #e5e6eb;
            border-top: 2px solid #165dff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .arco-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 400;
        }
        
        .arco-tag-blue {
            background: #e8f3ff;
            color: #165dff;
        }
        
        .arco-tag-green {
            background: #e8ffea;
            color: #00b42a;
        }
        
        .arco-tag-orange {
            background: #fff7e8;
            color: #ff7d00;
        }
        
        .arco-tag-red {
            background: #ffece8;
            color: #f53f3f;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="arco-card">
            <div class="arco-card-header">
                <h3 class="arco-card-title">订单搜索</h3>
            </div>
            <div class="arco-card-body">
                <div class="arco-form">
                    <div class="arco-form-item">
                        <label class="arco-form-item-label">店铺</label>
                        <select class="arco-select" id="shopSelector">
                            <option value="">请选择店铺</option>
                            <option value="228120078">短云游戏旗舰店</option>
                        </select>
                    </div>
                    <div class="arco-form-item">
                        <label class="arco-form-item-label">达人ID (可选)</label>
                        <input type="text" class="arco-input" id="authorId" placeholder="留空获取所有订单，或输入达人ID筛选">
                    </div>
                    <div class="arco-form-item">
                        <label class="arco-form-item-label">开始时间</label>
                        <input type="date" class="arco-input" id="startTime">
                    </div>
                    <div class="arco-form-item">
                        <label class="arco-form-item-label">结束时间</label>
                        <input type="date" class="arco-input" id="endTime">
                    </div>
                    <div class="arco-form-item">
                        <button class="arco-btn" onclick="searchOrders()">
                            <span id="loadingSpinner" class="arco-spin" style="display: none;"></span>
                            搜索订单
                        </button>
                    </div>
                    <div class="arco-form-item">
                        <button class="arco-btn arco-btn-outline" onclick="loadAllOrders()">
                            显示全部订单
                        </button>
                    </div>
                    <div class="arco-form-item">
                        <button class="arco-btn arco-btn-secondary" onclick="clearForm()">清空</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计面板 -->
        <div id="statsPanel" class="stats-panel">
            <div class="stats-header">
                <span>📊</span>
                <span id="statsTitle">达人统计</span>
            </div>
            <div class="stats-grid">
                <div class="stats-item">
                    <div class="stats-label">总订单数</div>
                    <div class="stats-value info" id="totalOrders">0</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">已使用</div>
                    <div class="stats-value success" id="usedCount">0</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">待使用</div>
                    <div class="stats-value warning" id="unusedCount">0</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">已关闭</div>
                    <div class="stats-value danger" id="closedCount">0</div>
                </div>
                <div class="stats-item">
                    <div class="stats-label">总金额</div>
                    <div class="stats-value info" id="totalAmount">¥0.00</div>
                </div>
            </div>
            <div class="status-breakdown">
                <div class="status-breakdown-title">状态详细分布</div>
                <div id="statusBreakdown"></div>
            </div>
        </div>

        <div class="arco-card">
            <div class="arco-card-header">
                <h3 class="arco-card-title">订单列表</h3>
            </div>
            <div class="arco-card-body">
                <div id="orderList">
                    <div class="arco-empty">
                        <div>点击搜索获取订单列表</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 分页状态
        let currentPage = 0;
        let pageSize = 100;
        let totalCount = 0;
        let hasMore = false;
        let currentSearchParams = {};
        let pageSizeOptions = [50, 100, 200, 500, 1000];

        // 搜索订单
        function searchOrders(page = 0) {
            const shopId = document.getElementById('shopSelector').value;
            const authorId = document.getElementById('authorId').value.trim();
            const startTime = document.getElementById('startTime').value;
            const endTime = document.getElementById('endTime').value;

            if (!shopId) {
                alert('请选择店铺');
                return;
            }

            // 保存搜索参数
            currentSearchParams = { shopId, authorId, startTime, endTime };
            currentPage = page;

            document.getElementById('loadingSpinner').style.display = 'inline-block';
            document.getElementById('orderList').innerHTML = `
                <div class="arco-empty">
                    <div class="arco-spin" style="margin: 0 auto 16px;"></div>
                    <div>正在搜索订单...</div>
                </div>
            `;

            const formData = new FormData();
            formData.append('shop_id', shopId);
            formData.append('author_id', authorId);
            formData.append('page', page.toString());
            formData.append('size', pageSize.toString());
            
            if (startTime) {
                formData.append('start_time', startTime);
            }
            if (endTime) {
                formData.append('end_time', endTime);
            }

            fetch('api.php?action=searchList', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('HTTP状态:', response.status);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                document.getElementById('loadingSpinner').style.display = 'none';
                console.log('API响应:', data);

                if (data.success) {
                    // 更新分页状态
                    totalCount = data.data.total || 0;
                    hasMore = data.data.has_more || false;

                    // 显示统计信息（如果有）
                    if (data.data.author_stats) {
                        renderAuthorStats(data.data.author_stats);
                    } else {
                        hideAuthorStats();
                    }

                    renderOrderList(data.data);
                } else {
                    document.getElementById('orderList').innerHTML = `
                        <div class="arco-empty">
                            <div style="color: #f53f3f;">搜索失败: ${data.message || '未知错误'}</div>
                        </div>
                    `;
                }
            })
            .catch(error => {
                document.getElementById('loadingSpinner').style.display = 'none';
                console.error('搜索错误:', error);
                document.getElementById('orderList').innerHTML = `
                    <div class="arco-empty">
                        <div style="color: #f53f3f;">网络错误: ${error.message}</div>
                    </div>
                `;
            });
        }

        // 渲染订单列表
        function renderOrderList(data) {
            if (!data.list || data.list.length === 0) {
                document.getElementById('orderList').innerHTML = `
                    <div class="arco-empty">
                        <div>未找到相关订单</div>
                    </div>
                `;
                return;
            }

            let html = '<div class="order-cards">';

            data.list.forEach(item => {
                const orderId = item.order_id || 'N/A';
                const orderAmount = item.order_amount ? (item.order_amount / 100).toFixed(2) : '0.00';
                const orderStatus = item.order_status_desc || '未知状态'; // 直接使用API返回的状态描述
                const createTime = item.create_time ? new Date(item.create_time * 1000).toLocaleString('zh-CN') : 'N/A';

                // 获取第一个SKU的信息（通常一个订单包含多个SKU）
                const firstSku = item.sku_order_list && item.sku_order_list.length > 0 ? item.sku_order_list[0] : {};

                // 商品信息
                const productName = firstSku.product_name || '未知商品';
                const productId = firstSku.product_id || firstSku.product_id_str || 'N/A';
                const productImage = firstSku.product_pic || '';

                // 推广信息（达人信息在SKU中）
                const authorId = firstSku.author_id || 'N/A';
                const authorName = firstSku.author_name || 'N/A';
                const cBizDesc = firstSku.c_biz_desc || 'N/A';

                // 状态样式
                const statusClass = getStatusClass(orderStatus);

                html += `
                    <div class="order-card">
                        <div class="order-header">
                            <div class="order-id">${orderId}</div>
                            <div class="order-amount">¥${orderAmount}</div>
                        </div>

                        <div class="order-content">
                            <div class="order-left">
                                <div class="product-info">
                                    ${productImage ? `<img src="${productImage}" alt="商品图片" class="product-image" onerror="this.style.display='none'">` : ''}
                                    <div class="product-details">
                                        <div class="product-name">${productName}</div>
                                        <div class="order-time">${createTime}</div>
                                    </div>
                                </div>
                            </div>
                            <div class="order-right">
                                <div class="order-status ${statusClass}">${orderStatus}</div>
                                <div class="author-info">
                                    ID: ${authorId}<br>
                                    ${authorName}
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';

            // 显示统计信息
            const totalCount = data.total || 0;
            const currentCount = data.list.length;
            const hasMore = data.has_more;
            const isFiltered = data.is_filtered || false;

            let statsText = `显示 ${currentCount} 条订单`;

            if (isFiltered && data.filtered_count !== null && data.original_count !== null) {
                // 筛选模式：显示筛选结果
                statsText = `找到 ${data.filtered_count} 条匹配订单 (从 ${data.original_count} 条中筛选)`;
            } else if (totalCount > currentCount) {
                // 分页模式：显示分页信息
                statsText += ` / 总共 ${totalCount} 条`;
                if (hasMore) {
                    statsText += ` (还有更多数据)`;
                }
            }

            html += `
                <div style="margin-top: 24px; text-align: center; color: #86909c; font-size: 14px;">
                    ${statsText}
                </div>
            `;

            // 添加专业分页组件（只在非筛选模式下显示）
            if (totalCount > pageSize && !isFiltered) {
                html += renderPagination();
            }

            document.getElementById('orderList').innerHTML = html;
        }

        // 获取状态样式类
        function getStatusClass(status) {
            const statusText = status.toLowerCase();
            if (statusText.includes('完成') || statusText.includes('成功')) return 'status-completed';
            if (statusText.includes('发货') || statusText.includes('已发')) return 'status-shipped';
            if (statusText.includes('支付') || statusText.includes('已付')) return 'status-paid';
            if (statusText.includes('待') || statusText.includes('等待')) return 'status-pending';
            if (statusText.includes('关闭') || statusText.includes('已关')) return 'status-closed';
            if (statusText.includes('取消') || statusText.includes('已取')) return 'status-cancelled';
            if (statusText.includes('使用') || statusText.includes('待使')) return 'status-using';
            return 'status-default';
        }

        // 渲染专业分页组件
        function renderPagination() {
            const totalPages = Math.ceil(totalCount / pageSize);
            const currentPageNum = currentPage + 1;
            const startRecord = currentPage * pageSize + 1;
            const endRecord = Math.min((currentPage + 1) * pageSize, totalCount);

            let paginationHtml = `
                <div class="pagination-container">
                    <div class="pagination-info">
                        显示第 ${startRecord}-${endRecord} 条，共 ${totalCount} 条记录
                    </div>

                    <div style="display: flex; align-items: center; gap: 16px;">
                        <div class="pagination-size-selector">
                            <span>每页显示</span>
                            <select class="pagination-size-select" onchange="changePageSize(this.value)">
            `;

            // 页面大小选项
            pageSizeOptions.forEach(size => {
                paginationHtml += `<option value="${size}" ${size === pageSize ? 'selected' : ''}>${size} 条</option>`;
            });

            paginationHtml += `
                            </select>
                        </div>

                        <div class="pagination">
            `;

            // 上一页按钮
            paginationHtml += `
                <button class="pagination-btn" onclick="goToPage(${currentPage - 1})" ${currentPage <= 0 ? 'disabled' : ''}>
                    ‹
                </button>
            `;

            // 页码按钮
            const pageNumbers = generatePageNumbers(currentPageNum, totalPages);
            pageNumbers.forEach(item => {
                if (item === '...') {
                    paginationHtml += `<span class="pagination-ellipsis">...</span>`;
                } else {
                    const isActive = item === currentPageNum;
                    paginationHtml += `
                        <button class="pagination-btn ${isActive ? 'active' : ''}"
                                onclick="goToPage(${item - 1})"
                                ${isActive ? 'disabled' : ''}>
                            ${item}
                        </button>
                    `;
                }
            });

            // 下一页按钮
            paginationHtml += `
                <button class="pagination-btn" onclick="goToPage(${currentPage + 1})" ${currentPage >= totalPages - 1 ? 'disabled' : ''}>
                    ›
                </button>
            `;

            // 跳转功能
            paginationHtml += `
                        </div>

                        <div class="pagination-jump">
                            <span>跳至</span>
                            <input type="number" class="pagination-jump-input"
                                   min="1" max="${totalPages}"
                                   placeholder="${currentPageNum}"
                                   onkeypress="handleJumpKeyPress(event, ${totalPages})">
                            <button class="pagination-jump-btn" onclick="jumpToPage(${totalPages})">确定</button>
                        </div>
                    </div>
                </div>
            `;

            return paginationHtml;
        }

        // 生成页码数组
        function generatePageNumbers(current, total) {
            const pages = [];
            const showPages = 5; // 显示的页码数量

            if (total <= showPages + 2) {
                // 总页数较少，显示所有页码
                for (let i = 1; i <= total; i++) {
                    pages.push(i);
                }
            } else {
                // 总页数较多，智能显示页码
                pages.push(1);

                if (current <= 3) {
                    for (let i = 2; i <= Math.min(showPages, total - 1); i++) {
                        pages.push(i);
                    }
                    if (total > showPages) {
                        pages.push('...');
                    }
                } else if (current >= total - 2) {
                    pages.push('...');
                    for (let i = Math.max(total - showPages + 1, 2); i <= total - 1; i++) {
                        pages.push(i);
                    }
                } else {
                    pages.push('...');
                    for (let i = current - 1; i <= current + 1; i++) {
                        pages.push(i);
                    }
                    pages.push('...');
                }

                if (total > 1) {
                    pages.push(total);
                }
            }

            return pages;
        }

        // 分页跳转
        function goToPage(page) {
            if (page < 0) return;
            searchOrders(page);
        }

        // 改变页面大小
        function changePageSize(newSize) {
            pageSize = parseInt(newSize);
            currentPage = 0; // 重置到第一页
            searchOrders(0);
        }

        // 跳转到指定页面
        function jumpToPage(totalPages) {
            const input = document.querySelector('.pagination-jump-input');
            const page = parseInt(input.value);
            if (page >= 1 && page <= totalPages) {
                goToPage(page - 1);
                input.value = '';
            } else {
                alert(`请输入 1-${totalPages} 之间的页码`);
            }
        }

        // 处理跳转输入框回车事件
        function handleJumpKeyPress(event, totalPages) {
            if (event.key === 'Enter') {
                jumpToPage(totalPages);
            }
        }

        // 加载全部订单
        function loadAllOrders() {
            // 设置大页面大小来显示更多订单
            const originalPageSize = pageSize;
            pageSize = 1000; // 临时设置为1000

            // 清空达人ID筛选，显示所有订单
            document.getElementById('authorId').value = '';

            // 执行搜索
            searchOrders(0);

            // 恢复原始页面大小
            pageSize = originalPageSize;
        }

        // 渲染达人统计信息
        function renderAuthorStats(stats) {
            document.getElementById('statsPanel').style.display = 'block';
            document.getElementById('statsTitle').textContent = `达人统计 - ${stats.author_name} (ID: ${stats.author_id})`;

            // 更新统计数据
            document.getElementById('totalOrders').textContent = stats.total_orders;
            document.getElementById('usedCount').textContent = stats.used_count;
            document.getElementById('unusedCount').textContent = stats.unused_count;
            document.getElementById('closedCount').textContent = stats.closed_count;
            document.getElementById('totalAmount').textContent = `¥${stats.total_amount.toFixed(2)}`;

            // 渲染状态详细分布
            const breakdownHtml = Object.entries(stats.status_breakdown)
                .map(([status, count]) => `
                    <div class="status-item">
                        <span class="status-name">${status}</span>
                        <span class="status-count">${count} 单</span>
                    </div>
                `).join('');

            document.getElementById('statusBreakdown').innerHTML = breakdownHtml;
        }

        // 隐藏达人统计信息
        function hideAuthorStats() {
            document.getElementById('statsPanel').style.display = 'none';
        }



        // 清空表单
        function clearForm() {
            document.getElementById('authorId').value = '';
            document.getElementById('startTime').value = '';
            document.getElementById('endTime').value = '';
            document.getElementById('orderList').innerHTML = `
                <div class="arco-empty">
                    <div>点击搜索获取订单列表</div>
                </div>
            `;
        }

        // 页面加载完成后设置默认店铺
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('shopSelector').value = '228120078';
        });
    </script>
</body>
</html>
