# 抖店Token自动刷新任务
# 每天凌晨2点检查并刷新即将过期的token
0 2 * * * /usr/bin/php /path/to/your/project/scripts/refresh_all_tokens.php >> /path/to/your/project/logs/token_refresh.log 2>&1

# 使用说明：
# 1. 将 /path/to/your/project 替换为你的实际项目路径
# 2. 确保PHP路径正确（可以用 which php 查看）
# 3. 确保logs目录有写权限
# 4. 使用 crontab -e 编辑cron任务
# 5. 使用 crontab -l 查看当前任务

# 示例：如果项目在 /var/www/douyin-aftersale
# 0 2 * * * /usr/bin/php /var/www/douyin-aftersale/scripts/refresh_all_tokens.php >> /var/www/douyin-aftersale/logs/token_refresh.log 2>&1
