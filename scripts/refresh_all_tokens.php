<?php
/**
 * 自动刷新所有店铺的token
 * 可以通过cron定期执行
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../src/Services/TokenManager.php';

$config = require __DIR__ . '/../config/config.php';
$tokenManager = new TokenManager($config);

// 获取所有配置的店铺
$shops = array_keys($config['shops']);

echo "[" . date('Y-m-d H:i:s') . "] 开始刷新所有店铺token..." . PHP_EOL;

$successCount = 0;
$failCount = 0;

foreach ($shops as $shopId) {
    $shopName = $config['shops'][$shopId]['name'];
    echo "正在处理店铺: {$shopName} (ID: {$shopId})" . PHP_EOL;
    
    // 检查token文件
    $tokenFile = __DIR__ . "/../storage/tokens_{$shopId}.json";
    if (!file_exists($tokenFile)) {
        echo "  ⚠️  没有找到token文件，跳过" . PHP_EOL;
        continue;
    }
    
    // 检查token是否即将过期（24小时内）
    $tokenData = json_decode(file_get_contents($tokenFile), true);
    $currentTime = time();
    $expiresAt = $tokenData['expires_at'] ?? 0;
    $timeToExpire = $expiresAt - $currentTime;
    
    if ($timeToExpire > 24 * 3600) {
        echo "  ✓ Token还有 " . round($timeToExpire / 3600, 1) . " 小时过期，无需刷新" . PHP_EOL;
        $successCount++;
        continue;
    }
    
    try {
        if ($timeToExpire <= 0) {
            echo "  🔄 Token已过期，尝试刷新..." . PHP_EOL;
        } else {
            echo "  🔄 Token即将过期，提前刷新..." . PHP_EOL;
        }
        
        // 尝试刷新token
        $refreshToken = $tokenData['refresh_token'] ?? '';
        if ($refreshToken) {
            $newTokenData = $tokenManager->refreshToken($refreshToken);
            $newTokenData['shop_id'] = $shopId;
            file_put_contents($tokenFile, json_encode($newTokenData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
            echo "  ✅ Token刷新成功，新过期时间: " . date('Y-m-d H:i:s', $newTokenData['expires_at']) . PHP_EOL;
        } else {
            throw new Exception("没有refresh_token");
        }
        
        $successCount++;
        
    } catch (Exception $e) {
        echo "  ❌ Token刷新失败: " . $e->getMessage() . PHP_EOL;
        
        // 尝试重新获取token
        try {
            echo "  🔄 尝试重新获取token..." . PHP_EOL;
            $newTokenData = $tokenManager->createTokenByShopId($shopId);
            echo "  ✅ Token重新获取成功，过期时间: " . date('Y-m-d H:i:s', $newTokenData['expires_at']) . PHP_EOL;
            $successCount++;
        } catch (Exception $e2) {
            echo "  ❌ Token重新获取失败: " . $e2->getMessage() . PHP_EOL;
            $failCount++;
        }
    }
    
    echo "" . PHP_EOL;
}

echo "[" . date('Y-m-d H:i:s') . "] Token刷新完成!" . PHP_EOL;
echo "成功: {$successCount} 个店铺" . PHP_EOL;
echo "失败: {$failCount} 个店铺" . PHP_EOL;

if ($failCount > 0) {
    exit(1); // 有失败的情况，返回错误码
}
?>
