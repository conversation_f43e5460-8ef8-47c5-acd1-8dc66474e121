<?php

//auto generated code
class autoload
{


	public static function loadClass($class)
	{
		$rootPath = dirname(__FILE__);
		$filename = $rootPath."/open/core/http/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/core/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/token/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/token/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/token/data/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/utils/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/spi/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_deliverTimeChangeConfirm/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_deliverTimeChangeConfirm/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_pushOnlineServiceConfig/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_pushOnlineServiceConfig/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_batchDecrypt/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_batchDecrypt/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_mergeV2/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_mergeV2/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_BatchSearchIndex/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_BatchSearchIndex/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_newCreateOrder/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_newCreateOrder/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_deliverTimeChangeTaskInfo/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_deliverTimeChangeTaskInfo/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_updateOrder/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_updateOrder/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_review/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_review/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_orderDetail/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_orderDetail/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_cancelOrder/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_cancelOrder/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_getCustomTemplateList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_getCustomTemplateList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_deliveryNotice/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_deliveryNotice/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_merge/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_merge/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_getServiceList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_getServiceList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_waybillApply/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_waybillApply/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_customTemplateList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_customTemplateList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_getOutRange/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_getOutRange/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_serviceDetail/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_serviceDetail/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_getMCToken/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_getMCToken/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_getRecommendedAndDeliveryExpressByOrder/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_getRecommendedAndDeliveryExpressByOrder/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_createSFOrder/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_createSFOrder/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_fetchBluetoothCmd/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_fetchBluetoothCmd/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_ordeReportList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_ordeReportList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_getDesignTemplateList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_getDesignTemplateList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_searchList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_searchList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_getShopKey/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_getShopKey/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_appendSubOrder/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_appendSubOrder/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_listShopNetsite/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_listShopNetsite/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_searchByReceiver/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_searchByReceiver/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_batchEncrypt/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_batchEncrypt/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_templateList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_templateList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_replyService/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_replyService/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/open_openId_replace/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/open_openId_replace/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_getProvince/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_getProvince/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_Detail/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_Detail/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_logisticsAddSinglePack/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_logisticsAddSinglePack/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/token_refresh/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/token_refresh/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_CategoryDimList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_CategoryDimList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/shop_getShopCategory/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/shop_getShopCategory/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_logisticsAddMultiPack/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_logisticsAddMultiPack/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/open_materialToken/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/open_materialToken/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_logisticsEdit/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_logisticsEdit/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_uploadextrapackage/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_uploadextrapackage/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/open_binaryupload/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/open_binaryupload/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_rejectReasonCodeList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_rejectReasonCodeList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_registerPackageRoute/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_registerPackageRoute/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_getCatePropertyV2/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_getCatePropertyV2/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_getMCToken/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_getMCToken/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_logisticsEditByPack/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_logisticsEditByPack/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_searchMaterial/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_searchMaterial/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_getFolderInfo/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_getFolderInfo/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_logisticsAdd/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_logisticsAdd/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_queryOrderLogistics/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_queryOrderLogistics/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_getProductShopRule/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_getProductShopRule/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_areaList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_areaList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_provinceList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_provinceList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/openCloud_v1_imageVersion_create/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/openCloud_v1_imageVersion_create/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_getAreasByProvince/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_getAreasByProvince/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/open_getAuthInfo/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/open_getAuthInfo/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_queryPackageRoute/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_queryPackageRoute/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_List/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_List/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_mergeV2/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_mergeV2/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_queryMaterialDetail/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_queryMaterialDetail/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_searchFolder/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_searchFolder/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_merge/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_merge/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/token_create/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/token_create/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_getUserDeliverServiceInfos/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_getUserDeliverServiceInfos/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_mGetPlayInfo/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_mGetPlayInfo/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_getSpuRule/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_getSpuRule/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_get_cap_info/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_get_cap_info/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_getAuditInfo/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_getAuditInfo/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_setAddr/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_setAddr/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_OpenAfterSaleChannel/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_OpenAfterSaleChannel/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_batchBindSerialNumber/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_batchBindSerialNumber/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_addressModify/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_addressModify/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_updatePostAmount/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_updatePostAmount/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_updateOrderAmount/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_updateOrderAmount/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_list/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_list/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_salesInherit_submitList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_salesInherit_submitList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_openOutAfterSale/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_openOutAfterSale/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_verifyV2/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_verifyV2/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_PackageProcessFail/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_PackageProcessFail/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_addressConfirm/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_addressConfirm/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_addresSwitchConfig/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_addresSwitchConfig/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_orderCancel/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_orderCancel/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_salesInherit_submit/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_salesInherit_submit/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_addOrderRemark/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_addOrderRemark/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_timeExtend/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_timeExtend/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_create/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_create/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_applyLogisticsIntercept/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_applyLogisticsIntercept/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_submitEvidence/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_submitEvidence/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_operate/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_operate/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_salesInherit_permission/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_salesInherit_permission/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_extendCertValidEndByOrder/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_extendCertValidEndByOrder/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_certVerifyUpdate/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_certVerifyUpdate/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_CancelSendGoodsSuccess/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_CancelSendGoodsSuccess/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_PackageProcessSuccess/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_PackageProcessSuccess/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_CertSyncFailedCancelOrder/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_CertSyncFailedCancelOrder/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_edit/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_edit/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_info/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_info/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_syncV2/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_syncV2/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_trackNoRouteDetail/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/logistics_trackNoRouteDetail/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/topup_result/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/topup_result/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_cancelVerify/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_cancelVerify/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_abandon/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_abandon/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_AddressAppliedSwitch/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_AddressAppliedSwitch/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_list/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_list/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/topup_accountTemplateList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/topup_accountTemplateList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_addSerialNumber/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_addSerialNumber/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_fillLogistics/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_fillLogistics/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_returnGoodsToWareHouseSuccess/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/afterSale_returnGoodsToWareHouseSuccess/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/shop_brandList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/shop_brandList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_batchUploadVideoAsync/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_batchUploadVideoAsync/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_queryShopSelfAuthors/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_queryShopSelfAuthors/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/brand_getSug/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/brand_getSug/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_listChannelProduct/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_listChannelProduct/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_logisticsCompanyList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_logisticsCompanyList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_queryShopAllianceProducts/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_queryShopAllianceProducts/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_exclusivePlanAuthorOperate/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_exclusivePlanAuthorOperate/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_bindChannelProduct/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_bindChannelProduct/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_channelBindAuthors/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_channelBindAuthors/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_update/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_update/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_qualityTask/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_qualityTask/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_uploadVideoAsync/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_uploadVideoAsync/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_previewChannelProduct/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_previewChannelProduct/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_strategyPromotionCreate/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_strategyPromotionCreate/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_create/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_create/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_qualityList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_qualityList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_updateChannelProduct/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_updateChannelProduct/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_createBatch/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_createBatch/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_onlineChannelProduct/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_onlineChannelProduct/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_addChannelProduct/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_addChannelProduct/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_qualityDetail/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_qualityDetail/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_offlineChannelProduct/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_offlineChannelProduct/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_queryLogisticsCompanyList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_queryLogisticsCompanyList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_promotionStrategyList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_promotionStrategyList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_allKolStrategyCreate/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_allKolStrategyCreate/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_simplePlan/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_simplePlan/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/brand_convert/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/brand_convert/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_createV2/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_createV2/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_removeAddr/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_removeAddr/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_list/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_list/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_uploadImageSync/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_uploadImageSync/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_orientKolStrategyCreate/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_orientKolStrategyCreate/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_kolStrategyEdit/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/buyin_kolStrategyEdit/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_setPriority/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_setPriority/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_removeChannelProduct/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_removeChannelProduct/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/brand_list/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/brand_list/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/promise_setSkuShipTime/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/promise_setSkuShipTime/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_setAddrBatch/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/warehouse_setAddrBatch/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/shop_reputation/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/shop_reputation/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/instantShopping_trade_freightCreate/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/instantShopping_trade_freightCreate/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/instantShopping_trade_freightDetail/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/instantShopping_trade_freightDetail/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/instantShopping_trade_freightList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/instantShopping_trade_freightList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_batchSensitive/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_batchSensitive/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/freightTemplate_list/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/freightTemplate_list/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/freightTemplate_create/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/freightTemplate_create/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/freightTemplate_detail/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/freightTemplate_detail/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/freightTemplate_update/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/freightTemplate_update/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/instantShopping_trade_freightUpdate/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/instantShopping_trade_freightUpdate/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_auditList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_auditList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_moveMaterialToRecycleBin/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_moveMaterialToRecycleBin/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/QA_xz002/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/QA_xz002/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_recoverMaterial/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_recoverMaterial/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_queryBookNameByISBN/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_queryBookNameByISBN/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_getSpuTpl/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_getSpuTpl/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sku_stockNum/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sku_stockNum/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_batchUploadImageSync/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_batchUploadImageSync/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_QuerySpu/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_QuerySpu/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_editFolder/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_editFolder/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_insurance/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_insurance/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_list/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_list/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_policy/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_policy/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_detail/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_detail/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_batchGetProductLocks/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_batchGetProductLocks/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_setOffline/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_setOffline/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_setOnline/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_setOnline/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_moveFolderToRecycleBin/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_moveFolderToRecycleBin/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_editMaterial/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_editMaterial/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_getSpu/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_getSpu/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_easyShuttle/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_easyShuttle/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_syncV2/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/coupons_syncV2/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sku_list/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sku_list/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_getRecommendName/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_getRecommendName/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/supplyCenter_cargo_getCategoryPropertyValue/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/supplyCenter_cargo_getCategoryPropertyValue/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_createFolder/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_createFolder/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_launchProduct/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_launchProduct/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_recoverFolder/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_recoverFolder/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_listV2/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_listV2/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sku_detail/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sku_detail/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_deleteFolder/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_deleteFolder/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_deleteMaterial/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/material_deleteMaterial/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_getSpuInfoBySpuId/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_getSpuInfoBySpuId/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_getKeyPropertyByCid/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_getKeyPropertyByCid/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_editV2/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_editV2/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_GetRecommendCategory/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_GetRecommendCategory/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_getSettleBillDetail/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_getSettleBillDetail/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_downloadShopAccountItemFile/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_downloadShopAccountItemFile/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/promise_deliveryList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/promise_deliveryList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_addV2/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_addV2/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_delProductMainPicVideo/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_delProductMainPicVideo/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_applyMainPicVideo/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_applyMainPicVideo/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_editCbProduct/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_editCbProduct/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_cancelAudit/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_cancelAudit/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_addSchema/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_addSchema/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_diagnoseCheck/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_diagnoseCheck/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sku_syncStockBatch/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sku_syncStockBatch/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_getPublishProductLimit/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_getPublishProductLimit/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_getComponentTemplate/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_getComponentTemplate/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_downloadToShop/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_downloadToShop/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_downloadSettleItemToShop/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_downloadSettleItemToShop/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_addShopSpu/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_addShopSpu/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_qualificationConfig/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_qualificationConfig/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_editBuyerLimit/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_editBuyerLimit/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_getProductUpdateRule/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_getProductUpdateRule/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_getSettleBillDetailV3/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_getSettleBillDetailV3/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_auditAutoRectifyGrant/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_auditAutoRectifyGrant/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_recommendProperties/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_recommendProperties/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_batchUploadImg/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_batchUploadImg/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_getSchema/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_getSchema/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_getShopAccountItemFile/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_getShopAccountItemFile/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sku_editPrice/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sku_editPrice/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_del/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_del/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_getCategoryPropertyValue/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_getCategoryPropertyValue/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_datchDelComponentTemplate/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_datchDelComponentTemplate/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_editComponentTemplate/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_editComponentTemplate/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sku_editCode/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sku_editCode/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_addCbProduct/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_addCbProduct/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_editSchema/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_editSchema/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sku_syncStockBatchMultiProducts/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sku_syncStockBatchMultiProducts/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sku_syncStock/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sku_syncStock/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_publishPreCheck/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_publishPreCheck/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_createSpu/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/spu_createSpu/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_getShopAccountItem/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_getShopAccountItem/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_getSettleBillDetailV2/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_getSettleBillDetailV2/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_downloadShopAccountItem/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_downloadShopAccountItem/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_batchCreatePrettifyPic/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_batchCreatePrettifyPic/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_createComponentTemplateV2/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_createComponentTemplateV2/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/shop_getExperienceScore/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/shop_getExperienceScore/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_apply_list/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_apply_list/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/antispam_orderQuery/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/antispam_orderQuery/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_delAftersaleStrategy/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_delAftersaleStrategy/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/antispam_orderSend/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/antispam_orderSend/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sign_apply_list/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sign_apply_list/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sign_delete/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sign_delete/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_listStrategyBindProducts/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_listStrategyBindProducts/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_updateAftersaleStrategy/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_updateAftersaleStrategy/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_sellerOrderInfo/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_sellerOrderInfo/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_sellerCancleDistribute/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_sellerCancleDistribute/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/antispam_userLogin/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/antispam_userLogin/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_delete/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_delete/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_listAftersaleStrategy/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_listAftersaleStrategy/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_addAftersaleStrategy/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_addAftersaleStrategy/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/security_batchReportOrderSecurityEvent/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/security_batchReportOrderSecurityEvent/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_waybillCancel/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_waybillCancel/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_delAftersaleAddress/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_delAftersaleAddress/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sign_apply/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sign_apply/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sign_apply_revoke/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sign_apply_revoke/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_revoke/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_revoke/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_public_template/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_public_template/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_waybillReturn/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_waybillReturn/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_getSellerList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_getSellerList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_sellerDistribute/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_sellerDistribute/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_apply/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_apply/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_search/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_search/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_waybillUpdate/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_waybillUpdate/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_orderList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_orderList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_getCascadeValue/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_getCascadeValue/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_waybillGet/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_waybillGet/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_sellerOrderList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_sellerOrderList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_invoiceList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_invoiceList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sign_search/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sign_search/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_sellerSupplierList/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_sellerSupplierList/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_getAftersaleStrategy/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/address_getAftersaleStrategy/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_addOrderRemark/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_addOrderRemark/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_orderInfo/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_orderInfo/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_roleGet/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/iop_roleGet/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_send/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_send/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_delete/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_delete/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/security_batchReportOrderSecurityEvent/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/security_batchReportOrderSecurityEvent/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_public_template/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_public_template/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_isv_createProductFromSupplyPlatform/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_isv_createProductFromSupplyPlatform/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/token_create/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/token_create/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_batchSend/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_batchSend/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sign_apply_list/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sign_apply_list/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sign_apply_revoke/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sign_apply_revoke/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_revoke/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_revoke/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sign_search/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sign_search/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_isv_scanClue/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_isv_scanClue/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/rights_info/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/rights_info/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_getSearchIndex/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_getSearchIndex/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_apply/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_apply/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sendResult/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sendResult/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_apply_list/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_apply_list/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sign_delete/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_sign_delete/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/token_refresh/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/token_refresh/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_batchEncrypt/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_batchEncrypt/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_search/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/sms_template_search/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_batchSensitive/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/order_batchSensitive/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_isv_saveGoodsSupplyStatus/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}
		$filename = $rootPath."/open/api/product_isv_saveGoodsSupplyStatus/param/".$class.".php";
		if(is_file($filename)) {
			include $filename;
			return;
		}

	}
}
spl_autoload_register('\autoload::loadClass');