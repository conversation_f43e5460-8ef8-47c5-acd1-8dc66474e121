<?php

//auto generated code
class LogisticsCreateSFOrderParam
{

	public $order_id;

	public $pack_id;

	public $cargo_details;

	public $service_list;

	public $contact_info_list;

	public $pay_method;

	public $express_type_id;

	public $parcel_qty;

	public $total_weight;

	public $is_sign_back;

	public $remark;

	public $total_length;

	public $total_width;

	public $total_height;

	public $volume;

	public $user_id;

	public $order_channel;

}
