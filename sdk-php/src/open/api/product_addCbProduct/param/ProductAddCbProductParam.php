<?php

//auto generated code
class ProductAddCbProductParam
{

	public $outer_product_id;

	public $product_type;

	public $category_leaf_id;

	public $name;

	public $recommend_remark;

	public $pic;

	public $description;

	public $pay_type;

	public $delivery_method;

	public $cdf_category;

	public $reduce_type;

	public $assoc_ids;

	public $freight_id;

	public $weight;

	public $weight_unit;

	public $delivery_delay_day;

	public $presell_type;

	public $presell_delay;

	public $presell_end_time;

	public $supply_7day_return;

	public $mobile;

	public $commit;

	public $remark;

	public $out_product_id;

	public $quality_list;

	public $spec_name;

	public $specs;

	public $spec_prices;

	public $spec_pic;

	public $maximum_per_order;

	public $limit_per_buyer;

	public $minimum_per_order;

	public $product_format_new;

	public $spu_id;

	public $appoint_delivery_day;

	public $third_url;

	public $extra;

	public $src;

	public $need_check_out;

	public $poi_resource;

	public $car_vin_code;

	public $need_recharge_mode;

	public $presell_config_level;

	public $account_template_id;

	public $presell_delivery_type;

	public $white_back_ground_pic_url;

	public $long_pic_url;

	public $after_sale_service;

	public $sell_channel;

	public $start_sale_type;

	public $logistics_info;

	public $price_has_tax;

	public $biz_kind;

	public $standard_brand_id;

	public $discount_price;

	public $market_price;

	public $size_info_template_id;

}
