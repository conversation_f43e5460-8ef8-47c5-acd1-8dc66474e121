<?php

//auto generated code
class ProductAddV2Param
{

	public $outer_product_id;

	public $product_type;

	public $category_leaf_id;

	public $name;

	public $recommend_remark;

	public $pic;

	public $description;

	public $pay_type;

	public $delivery_method;

	public $cdf_category;

	public $reduce_type;

	public $assoc_ids;

	public $freight_id;

	public $weight;

	public $weight_unit;

	public $delivery_delay_day;

	public $presell_type;

	public $presell_delay;

	public $presell_end_time;

	public $supply_7day_return;

	public $mobile;

	public $commit;

	public $remark;

	public $out_product_id;

	public $quality_list;

	public $spec_name;

	public $specs;

	public $spec_prices;

	public $spec_pic;

	public $maximum_per_order;

	public $limit_per_buyer;

	public $minimum_per_order;

	public $product_format_new;

	public $spu_id;

	public $appoint_delivery_day;

	public $third_url;

	public $extra;

	public $src;

	public $standard_brand_id;

	public $need_check_out;

	public $poi_resource;

	public $car_vin_code;

	public $presell_config_level;

	public $need_recharge_mode;

	public $account_template_id;

	public $presell_delivery_type;

	public $white_back_ground_pic_url;

	public $long_pic_url;

	public $after_sale_service;

	public $sell_channel;

	public $start_sale_type;

	public $delay_rule;

	public $material_video_id;

	public $pickup_method;

	public $size_info_template_id;

	public $substitute_goods_url;

	public $sale_channel_type;

	public $recruit_info;

	public $store_id;

	public $main_product_id;

	public $sale_limit_id;

	public $name_prefix;

	public $reference_price;

	public $reference_price_certificate;

	public $main_image_three_to_four;

	public $unit_price_info;

	public $quality_inspection_info;

	public $is_c2b_switch_on;

	public $micro_app_id;

	public $is_auto_charge;

	public $short_product_name;

	public $after_sale_service_v2;

	public $spec_info;

	public $spec_prices_v2;

	public $with_sku_type;

	public $name_suffix;

	public $use_brand_name;

	public $open_logistics_info;

	public $price_has_tax;

	public $biz_kind;

	public $custom_property;

	public $is_evaluate_opened;

	public $default_process_time;

	public $company_id;

	public $is_second_hand_digital;

	public $recommend_ids;

}
