<?php

require_once __DIR__ . '/../../sdk-php/src/autoload.php';
require_once __DIR__ . '/TokenManager.php';

/**
 * 售后服务类
 * 负责处理售后列表查询和退款操作
 */
class AfterSaleService
{
    private $config;
    private $tokenManager;

    public function __construct($config)
    {
        $this->config = $config;
        $this->tokenManager = new TokenManager($config);

        // 注册自动加载
        spl_autoload_register(['autoload', 'loadClass']);
    }

    /**
     * 获取售后列表
     */
    public function getAfterSaleList($params = [])
    {
        try {
            // 获取店铺ID
            $shopId = $params['shop_id'] ?? $this->config['douyin']['shop_id'];

            // 获取有效的access_token对象
            $accessToken = $this->tokenManager->getAccessTokenObject($shopId);

            $request = new AfterSaleListRequest();
            $param = new AfterSaleListParam();

            // 设置查询参数
            if (isset($params['order_id'])) {
                $param->order_id = $params['order_id'];
            }
            if (isset($params['aftersale_id'])) {
                $param->aftersale_id = $params['aftersale_id'];
            }
            if (isset($params['aftersale_status'])) {
                $param->aftersale_status = $params['aftersale_status'];
            }
            if (isset($params['standard_aftersale_status'])) {
                $param->standard_aftersale_status = $params['standard_aftersale_status'];
            }
            if (isset($params['need_special_type'])) {
                $param->need_special_type = $params['need_special_type'];
            }
            if (isset($params['aftersale_type'])) {
                $param->aftersale_type = $params['aftersale_type'];
            }
            if (isset($params['start_time'])) {
                $param->start_time = $params['start_time'];
            }
            if (isset($params['end_time'])) {
                $param->end_time = $params['end_time'];
            }

            // 分页参数 - 抖音API的page从0开始
            $requestedPage = $params['page'] ?? 1;
            $param->page = max(0, $requestedPage - 1); // 转换为从0开始的页码
            $requestedSize = $params['size'] ?? $this->config['pagination']['default_page_size'];

            // 确保分页大小不超过最大限制
            if ($requestedSize > $this->config['pagination']['max_page_size']) {
                $requestedSize = $this->config['pagination']['max_page_size'];
            }

            $param->size = $requestedSize;

            $request->setParam($param);

            // 执行请求
            $response = $request->execute($accessToken);

            if ($response && $response->code == 10000) {
                // 检查是否返回了空数组但total > 0，如果是则尝试更小的size
                $items = $response->data->items ?? [];
                $total = $response->data->total ?? 0;

                if (empty($items) && $total > 0 && $param->size > 1 && $param->page == 1) {
                    // 尝试更小的size值
                    $smallerSizes = [10, 8, 6, 4, 3, 2, 1];
                    foreach ($smallerSizes as $smallerSize) {
                        if ($smallerSize < $param->size) {
                            $param->size = $smallerSize;
                            $request->setParam($param);
                            $retryResponse = $request->execute($accessToken);

                            if ($retryResponse && $retryResponse->code == 10000) {
                                $retryItems = $retryResponse->data->items ?? [];
                                if (!empty($retryItems)) {
                                    // 找到了有效的size，使用这个响应
                                    $response = $retryResponse;
                                    error_log("智能分页：店铺 $shopId 使用 size=$smallerSize 获取到 " . count($retryItems) . " 条记录");
                                    break;
                                }
                            }
                        }
                    }
                }

                $result = [
                    'success' => true,
                    'data' => $response->data,
                    'total' => $response->data->total ?? 0,
                    'page' => $param->page,
                    'size' => $param->size
                ];

                $this->logOperation('get_aftersale_list', $params, true);
                return $result;
            } else {
                $error = $response ? $response->message : '未知错误';
                $this->logOperation('get_aftersale_list', $params, false, $error);
                throw new Exception("获取售后列表失败: " . $error);
            }
        } catch (Exception $e) {
            $this->logOperation('get_aftersale_list', $params, false, $e->getMessage());
            throw $e;
        }
    }

    /**
     * 执行售后操作（退款）
     */
    public function operateAfterSale($aftersaleId, $operationType, $operationData = [])
    {
        try {
            // 获取店铺ID
            $shopId = $operationData['shop_id'] ?? $this->config['douyin']['shop_id'];

            // 获取有效的access_token对象
            $accessToken = $this->tokenManager->getAccessTokenObject($shopId);

            $request = new AfterSaleOperateRequest();
            $param = new AfterSaleOperateParam();

            $param->type = $operationType;

            // 构建操作项目数据 - 使用官方API格式
            $items = [];

            if ($operationType == 201) { // 同意退款
                $items[] = [
                    'aftersale_id' => $aftersaleId,
                    'evidence' => [
                        ['type' => 4] // 根据官方API示例
                    ]
                ];
                // 如果有退款金额，添加到item中
                if (isset($operationData['refund_amount'])) {
                    $items[0]['agree_refund_amount'] = $operationData['refund_amount'];
                }
            } elseif ($operationType == 202) { // 拒绝退款
                $items[] = [
                    'aftersale_id' => $aftersaleId,
                    'evidence' => [
                        ['type' => 4]
                    ]
                ];
                // 如果有拒绝原因，添加到item中
                if (isset($operationData['reject_reason'])) {
                    $items[0]['reject_reason'] = $operationData['reject_reason'];
                }
            }

            $param->items = $items;

            if (isset($operationData['store_id'])) {
                $param->store_id = $operationData['store_id'];
            }

            $request->setParam($param);

            // 执行请求
            $response = $request->execute($accessToken);

            if ($response && $response->code == 10000) {
                $result = [
                    'success' => true,
                    'data' => $response->data,
                    'message' => '操作成功'
                ];

                $this->logOperation('operate_aftersale', [
                    'aftersale_id' => $aftersaleId,
                    'operation_type' => $operationType,
                    'operation_data' => $operationData
                ], true);

                return $result;
            } else {
                $error = $response ? (isset($response->message) ? $response->message : '操作失败') : '未知错误';
                $this->logOperation('operate_aftersale', [
                    'aftersale_id' => $aftersaleId,
                    'operation_type' => $operationType,
                    'operation_data' => $operationData
                ], false, $error);
                throw new Exception("售后操作失败: " . $error);
            }
        } catch (Exception $e) {
            $this->logOperation('operate_aftersale', [
                'aftersale_id' => $aftersaleId,
                'operation_type' => $operationType,
                'operation_data' => $operationData
            ], false, $e->getMessage());
            throw $e;
        }
    }

    /**
     * 获取售后详情
     */
    public function getAfterSaleDetail($aftersaleId, $shopId = null)
    {
        try {
            // 获取店铺ID
            if (!$shopId) {
                $shopId = $this->config['douyin']['shop_id'];
            }

            // 获取有效的access_token对象
            $accessToken = $this->tokenManager->getAccessTokenObject($shopId);

            $request = new AfterSaleDetailRequest();
            $param = new AfterSaleDetailParam();
            $param->aftersale_id = $aftersaleId;

            $request->setParam($param);

            // 执行请求
            $response = $request->execute($accessToken);

            if ($response && $response->code == 10000) {
                $this->logOperation('get_aftersale_detail', ['aftersale_id' => $aftersaleId], true);
                return [
                    'success' => true,
                    'data' => $response->data
                ];
            } else {
                $error = $response ? $response->message : '未知错误';
                $this->logOperation('get_aftersale_detail', ['aftersale_id' => $aftersaleId], false, $error);
                throw new Exception("获取售后详情失败: " . $error);
            }
        } catch (Exception $e) {
            $this->logOperation('get_aftersale_detail', ['aftersale_id' => $aftersaleId], false, $e->getMessage());
            throw $e;
        }
    }

    /**
     * 格式化售后状态
     */
    public function formatAfterSaleStatus($status)
    {
        return $this->config['aftersale_status'][$status] ?? '未知状态';
    }

    /**
     * 格式化售后类型
     */
    public function formatAfterSaleType($type)
    {
        return $this->config['aftersale_type'][$type] ?? '未知类型';
    }

    /**
     * 记录操作日志
     */
    private function logOperation($operation, $data, $success, $error = null)
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'operation' => $operation,
            'success' => $success,
            'data' => $data,
            'error' => $error
        ];

        $logFile = $this->config['system']['log_file'] ?? __DIR__ . '/../../logs/app.log';
        $logDir = dirname($logFile);

        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        $logLine = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
    }


}