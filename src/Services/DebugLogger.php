<?php

/**
 * 调试日志记录器
 * 提供详细的调试信息记录功能
 */
class DebugLogger
{
    private $config;
    private $debugMode;
    private $logFile;
    private $debugLogFile;

    public function __construct($config)
    {
        $this->config = $config;
        $this->debugMode = $config['system']['debug_mode'] ?? false;
        $this->logFile = $config['system']['log_file'] ?? __DIR__ . '/../../logs/app.log';
        $this->debugLogFile = $config['system']['debug_log_file'] ?? __DIR__ . '/../../logs/debug.log';
        
        // 确保日志目录存在
        $this->ensureLogDirectory();
    }

    /**
     * 记录调试信息
     */
    public function debug($message, $data = null, $context = [])
    {
        if (!$this->debugMode) {
            return;
        }

        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => 'DEBUG',
            'message' => $message,
            'data' => $data,
            'context' => $context,
            'memory_usage' => $this->formatBytes(memory_get_usage()),
            'peak_memory' => $this->formatBytes(memory_get_peak_usage()),
            'execution_time' => $this->getExecutionTime()
        ];

        $this->writeLog($this->debugLogFile, $logData);

        // 只在CLI模式下输出到屏幕，避免破坏API响应
        if (php_sapi_name() === 'cli' && ($this->config['system']['show_errors'] ?? false)) {
            echo "[DEBUG] " . $message . "\n";
            if ($data) {
                echo "Data: " . json_encode($data, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
            }
        }
    }

    /**
     * 记录API调用信息
     */
    public function apiCall($method, $url, $params = [], $response = null, $duration = 0)
    {
        if (!($this->config['system']['api_debug'] ?? false)) {
            return;
        }

        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => 'API',
            'method' => $method,
            'url' => $url,
            'params' => $params,
            'response_size' => $response ? strlen(json_encode($response)) : 0,
            'duration_ms' => round($duration * 1000, 2),
            'memory_usage' => $this->formatBytes(memory_get_usage())
        ];

        // 如果响应太大，只记录部分信息
        if ($response && strlen(json_encode($response)) > 10000) {
            $logData['response'] = '[Large Response - ' . strlen(json_encode($response)) . ' bytes]';
        } else {
            $logData['response'] = $response;
        }

        $this->writeLog($this->debugLogFile, $logData);
    }

    /**
     * 记录错误信息
     */
    public function error($message, $exception = null, $context = [])
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => 'ERROR',
            'message' => $message,
            'context' => $context
        ];

        if ($exception instanceof Exception) {
            $logData['exception'] = [
                'message' => $exception->getMessage(),
                'file' => $exception->getFile(),
                'line' => $exception->getLine(),
                'trace' => $exception->getTraceAsString()
            ];
        }

        $this->writeLog($this->logFile, $logData);
        $this->writeLog($this->debugLogFile, $logData);
    }

    /**
     * 记录性能信息
     */
    public function performance($operation, $startTime, $data = [])
    {
        $duration = microtime(true) - $startTime;
        
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => 'PERFORMANCE',
            'operation' => $operation,
            'duration_ms' => round($duration * 1000, 2),
            'memory_usage' => $this->formatBytes(memory_get_usage()),
            'peak_memory' => $this->formatBytes(memory_get_peak_usage()),
            'data' => $data
        ];

        $this->writeLog($this->debugLogFile, $logData);
        
        // 如果操作耗时超过1秒，记录到主日志
        if ($duration > 1.0) {
            $this->writeLog($this->logFile, $logData);
        }
    }

    /**
     * 记录SQL查询（如果将来使用数据库）
     */
    public function sql($query, $params = [], $duration = 0)
    {
        if (!$this->debugMode) {
            return;
        }

        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'level' => 'SQL',
            'query' => $query,
            'params' => $params,
            'duration_ms' => round($duration * 1000, 2)
        ];

        $this->writeLog($this->debugLogFile, $logData);
    }

    /**
     * 获取执行时间
     */
    private function getExecutionTime()
    {
        if (defined('APP_START_TIME')) {
            return round((microtime(true) - APP_START_TIME) * 1000, 2) . 'ms';
        }
        return 'N/A';
    }

    /**
     * 格式化字节数
     */
    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= pow(1024, $pow);
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * 写入日志文件
     */
    private function writeLog($file, $data)
    {
        $logLine = json_encode($data, JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents($file, $logLine, FILE_APPEND | LOCK_EX);
    }

    /**
     * 确保日志目录存在
     */
    private function ensureLogDirectory()
    {
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
        
        $debugLogDir = dirname($this->debugLogFile);
        if (!is_dir($debugLogDir)) {
            mkdir($debugLogDir, 0755, true);
        }
    }

    /**
     * 清理旧日志文件
     */
    public function cleanOldLogs($days = 7)
    {
        $files = [
            $this->logFile,
            $this->debugLogFile
        ];

        foreach ($files as $file) {
            if (file_exists($file) && (time() - filemtime($file)) > ($days * 24 * 3600)) {
                // 备份旧日志
                $backupFile = $file . '.' . date('Y-m-d', filemtime($file));
                if (!file_exists($backupFile)) {
                    rename($file, $backupFile);
                }
            }
        }
    }

    /**
     * 获取调试统计信息
     */
    public function getStats()
    {
        return [
            'debug_mode' => $this->debugMode,
            'log_file_size' => file_exists($this->logFile) ? $this->formatBytes(filesize($this->logFile)) : '0 B',
            'debug_log_size' => file_exists($this->debugLogFile) ? $this->formatBytes(filesize($this->debugLogFile)) : '0 B',
            'memory_usage' => $this->formatBytes(memory_get_usage()),
            'peak_memory' => $this->formatBytes(memory_get_peak_usage()),
            'execution_time' => $this->getExecutionTime()
        ];
    }
}
?>
