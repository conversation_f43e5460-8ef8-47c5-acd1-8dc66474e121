<?php

require_once __DIR__ . '/../../sdk-php/src/autoload.php';

class OrderService
{
    private $config;
    private $tokenManager;

    public function __construct($config)
    {
        $this->config = $config;
        $this->tokenManager = new TokenManager($config);
    }

    /**
     * 搜索订单列表
     */
    public function searchOrderList($params = [])
    {
        try {
            // 获取店铺ID
            $shopId = $params['shop_id'] ?? $this->config['douyin']['shop_id'];

            // 获取有效的access_token对象
            $accessToken = $this->tokenManager->getAccessTokenObject($shopId);

            $request = new OrderSearchListRequest();
            $param = new OrderSearchListParam();

            // 设置基本参数
            $param->page = strval($params['page'] ?? 0);
            // 如果是筛选达人，获取更多数据用于统计
            $param->size = intval($params['size'] ?? (isset($params['author_id']) && !empty($params['author_id']) ? 1000 : 100));
            $param->order_by = 'create_time';
            $param->order_asc = 'false';

            // 设置时间范围 - 8月1日以后
            $param->create_time_start = strtotime('2025-08-01 00:00:00');
            $param->create_time_end = time();

            $request->setParam($param);

            // 执行请求
            $response = $request->execute($accessToken);



            if ($response && isset($response->data)) {
                $allItems = $response->data->shop_order_list ?? [];

                // 如果指定了达人ID，进行筛选并统计
                $filteredItems = $allItems;
                $authorStats = null;

                // 调试参数
                $debugParams = [
                    'has_author_id' => isset($params['author_id']),
                    'author_id_value' => $params['author_id'] ?? 'not_set',
                    'is_empty' => empty($params['author_id'] ?? ''),
                    'params_keys' => array_keys($params)
                ];

                if (isset($params['author_id']) && !empty($params['author_id'])) {
                    $searchAuthorId = $params['author_id'];

                    $filteredItems = array_filter($allItems, function($item) use ($searchAuthorId) {
                        // 检查 sku_order_list 中的达人信息
                        if (isset($item->sku_order_list) && is_array($item->sku_order_list)) {
                            foreach ($item->sku_order_list as $sku) {
                                if (isset($sku->author_id)) {
                                    $skuAuthorId = $sku->author_id;
                                    if ($skuAuthorId == $searchAuthorId ||
                                        strval($skuAuthorId) === strval($searchAuthorId) ||
                                        intval($skuAuthorId) === intval($searchAuthorId)) {
                                        return true;
                                    }
                                }
                            }
                        }
                        return false;
                    });
                    $filteredItems = array_values($filteredItems); // 重新索引

                    // 获取该达人的全量统计（8月1日以后）
                    $authorStats = $this->getAuthorStatsFromAugust($params['author_id'], $shopId, $accessToken);
                }

                $this->logOperation('search_order_list', [
                    'shop_id' => $shopId,
                    'params' => $params,
                    'total_items' => count($allItems),
                    'filtered_items' => count($filteredItems),
                    'author_id_search' => $params['author_id'] ?? 'none'
                ], true);

                return [
                    'success' => true,
                    'data' => (object)[
                        'list' => $filteredItems,
                        'total' => $response->data->total ?? count($allItems),
                        'has_more' => $response->data->has_more ?? false,
                        'page' => $param->page,
                        'size' => $param->size,
                        'author_stats' => $authorStats,

                    ],
                    'message' => '获取成功'
                ];
            } else {
                $this->logOperation('search_order_list', [
                    'shop_id' => $shopId,
                    'params' => $params
                ], false, 'API响应为空');

                return [
                    'success' => false,
                    'message' => '抖店API暂时无响应，请稍后重试',
                    'debug_info' => [
                        'response_type' => gettype($response),
                        'response_data' => $response ? 'has_response' : 'null_response'
                    ]
                ];
            }

        } catch (\Exception $e) {
            $this->logOperation('search_order_list', [
                'shop_id' => $shopId ?? 'unknown',
                'params' => $params
            ], false, $e->getMessage());

            return [
                'success' => false,
                'message' => '搜索订单失败: ' . $e->getMessage(),
                'debug_info' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'error_class' => get_class($e)
                ]
            ];
        }
    }

    /**
     * 获取达人8月1日到现在的实时全量统计
     */
    private function getAuthorStatsFromAugust($authorId, $shopId, $accessToken) {
        // 设置更长的执行时间，查询全部22318个订单
        set_time_limit(900); // 15分钟

        try {
            $allAuthorOrders = [];
            $page = 0;
            $pageSize = 100;
            $maxPages = 200; // 查询前20000个订单，应该能接近3502个订单
            $currentTime = time();
            $augustStart = strtotime('2025-08-01 00:00:00');

            error_log("开始实时统计达人 {$authorId}，时间范围：" . date('Y-m-d H:i:s', $augustStart) . " 到 " . date('Y-m-d H:i:s', $currentTime));

            // 分页获取所有数据
            for ($page = 0; $page < $maxPages; $page++) {
                $request = new OrderSearchListRequest();
                $param = new OrderSearchListParam();

                $param->page = strval($page);
                $param->size = $pageSize;
                $param->order_by = 'create_time';
                $param->order_asc = 'false';
                $param->create_time_start = $augustStart;
                $param->create_time_end = $currentTime; // 使用当前时间，确保实时性

                $request->setParam($param);
                $response = $request->execute($accessToken);

                if ($response && isset($response->data)) {
                    $pageItems = $response->data->shop_order_list ?? [];

                    if (empty($pageItems)) {
                        error_log("第 {$page} 页没有数据，停止查询");
                        break;
                    }

                    error_log("第 {$page} 页获取到 " . count($pageItems) . " 条订单");

                    // 筛选该达人的订单
                    $pageAuthorOrders = array_filter($pageItems, function($item) use ($authorId) {
                        if (isset($item->sku_order_list) && is_array($item->sku_order_list)) {
                            foreach ($item->sku_order_list as $sku) {
                                if (isset($sku->author_id)) {
                                    $skuAuthorId = $sku->author_id;
                                    if ($skuAuthorId == $authorId ||
                                        strval($skuAuthorId) === strval($authorId) ||
                                        intval($skuAuthorId) === intval($authorId)) {
                                        return true;
                                    }
                                }
                            }
                        }
                        return false;
                    });

                    $pageAuthorOrdersCount = count($pageAuthorOrders);
                    if ($pageAuthorOrdersCount > 0) {
                        error_log("第 {$page} 页找到该达人的 {$pageAuthorOrdersCount} 个订单");
                    }

                    $allAuthorOrders = array_merge($allAuthorOrders, array_values($pageAuthorOrders));

                    // 如果这页数据少于pageSize，说明接近数据末尾
                    if (count($pageItems) < $pageSize) {
                        error_log("第 {$page} 页数据不足 {$pageSize} 条，可能接近数据末尾");
                        // 但继续查询几页确保完整性
                        if ($page > 50) { // 至少查询50页后才考虑停止
                            break;
                        }
                    }
                } else {
                    error_log("第 {$page} 页API调用失败");
                    break;
                }

                // 避免API限制，查询大量数据时需要更多休息
                if ($page > 0 && $page % 5 == 0) {
                    usleep(200000); // 每5页休息0.2秒
                }

                // 每50页休息更长时间
                if ($page > 0 && $page % 50 == 0) {
                    error_log("已查询 {$page} 页，休息1秒...");
                    sleep(1);
                }
            }

            $totalFound = count($allAuthorOrders);
            error_log("统计完成：共查询 {$page} 页，找到该达人的 {$totalFound} 个订单");

            // 计算完整统计
            $stats = $this->calculateAuthorStats($allAuthorOrders, $authorId);

            // 添加实时统计信息
            if ($stats) {
                $stats['debug_pages'] = $page;
                $stats['debug_total_found'] = $totalFound;
                $stats['time_range'] = '8月1日到现在（实时）';
                $stats['query_time'] = date('Y-m-d H:i:s', $currentTime);
                $stats['start_time'] = '2025-08-01 00:00:00';
            }

            return $stats;

        } catch (Exception $e) {
            error_log("获取达人实时统计失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取达人的完整统计信息（分页获取所有数据）
     */
    private function getCompleteAuthorStats($authorId, $shopId, $accessToken) {
        // 增加执行时间限制
        set_time_limit(300); // 5分钟

        try {
            $allAuthorOrders = [];
            $page = 0;
            $pageSize = 100; // 每页100条，避免超时
            $maxPages = 30; // 先查询30页，避免超时

            // 分页获取数据
            $consecutiveEmptyPages = 0;
            for ($page = 0; $page < $maxPages; $page++) {
                try {
                    $request = new OrderSearchListRequest();
                    $param = new OrderSearchListParam();

                    $param->page = strval($page);
                    $param->size = $pageSize;
                    $param->order_by = 'create_time';
                    $param->order_asc = 'false';
                    // 8月1日以后的订单
                    $param->create_time_start = strtotime('2025-08-01 00:00:00');
                    $param->create_time_end = time();

                    $request->setParam($param);
                    $response = $request->execute($accessToken);

                    if ($response && isset($response->data)) {
                        $pageItems = $response->data->shop_order_list ?? [];

                        if (empty($pageItems)) {
                            $consecutiveEmptyPages++;
                            if ($consecutiveEmptyPages >= 3) {
                                break; // 连续3页没有数据，停止查询
                            }
                            continue;
                        }

                        $consecutiveEmptyPages = 0; // 重置计数器

                        // 筛选该达人的订单
                        $pageAuthorOrders = array_filter($pageItems, function($item) use ($authorId) {
                            if (isset($item->sku_order_list) && is_array($item->sku_order_list)) {
                                foreach ($item->sku_order_list as $sku) {
                                    if (isset($sku->author_id)) {
                                        $skuAuthorId = $sku->author_id;
                                        if ($skuAuthorId == $authorId ||
                                            strval($skuAuthorId) === strval($authorId) ||
                                            intval($skuAuthorId) === intval($authorId)) {
                                            return true;
                                        }
                                    }
                                }
                            }
                            return false;
                        });

                        $pageAuthorOrdersArray = array_values($pageAuthorOrders);
                        $allAuthorOrders = array_merge($allAuthorOrders, $pageAuthorOrdersArray);

                        // 如果这页数据少于pageSize，说明可能接近数据末尾
                        if (count($pageItems) < $pageSize) {
                            // 但继续查询几页，确保没有遗漏
                            if ($page > 10) { // 至少查询10页后才考虑停止
                                break;
                            }
                        }
                    } else {
                        $consecutiveEmptyPages++;
                        if ($consecutiveEmptyPages >= 3) {
                            break;
                        }
                    }

                    // 每10页休息一下，避免API限制
                    if ($page > 0 && $page % 10 == 0) {
                        usleep(100000); // 休息0.1秒
                    }

                } catch (Exception $e) {
                    error_log("查询第{$page}页失败: " . $e->getMessage());
                    $consecutiveEmptyPages++;
                    if ($consecutiveEmptyPages >= 3) {
                        break;
                    }
                }
            }

            // 计算完整统计
            $stats = $this->calculateAuthorStats($allAuthorOrders, $authorId);

            // 添加调试信息
            if ($stats) {
                $stats['debug_pages'] = $page + 1;
                $stats['debug_total_found'] = count($allAuthorOrders);
            }

            return $stats;

        } catch (Exception $e) {
            error_log("获取达人完整统计失败: " . $e->getMessage());
            return null;
        }
    }

    /**
     * 获取达人的全部订单
     */
    private function getAllAuthorOrders($authorId, $shopId, $accessToken) {
        try {
            // 使用更大的页面大小获取更多数据
            $request = new OrderSearchListRequest();
            $param = new OrderSearchListParam();

            $param->page = '0';
            $param->size = 1000; // 获取1000条数据
            $param->order_by = 'create_time';
            $param->order_asc = 'false';
            $param->create_time_start = time() - (30 * 24 * 60 * 60);
            $param->create_time_end = time();

            $request->setParam($param);
            $response = $request->execute($accessToken);

            if ($response && isset($response->data)) {
                $allItems = $response->data->shop_order_list ?? [];

                // 筛选该达人的所有订单
                $authorOrders = array_filter($allItems, function($item) use ($authorId) {
                    if (isset($item->sku_order_list) && is_array($item->sku_order_list)) {
                        foreach ($item->sku_order_list as $sku) {
                            if (isset($sku->author_id) && $sku->author_id == $authorId) {
                                return true;
                            }
                        }
                    }
                    return false;
                });

                return array_values($authorOrders);
            }

            return [];
        } catch (Exception $e) {
            error_log("获取达人全部订单失败: " . $e->getMessage());
            return [];
        }
    }

    /**
     * 计算达人统计信息
     */
    private function calculateAuthorStats($orders, $authorId) {
        $stats = [
            'author_id' => $authorId,
            'author_name' => '',
            'total_orders' => count($orders),
            'total_amount' => 0,
            'status_breakdown' => [],
            'used_count' => 0,
            'unused_count' => 0,
            'closed_count' => 0,
            'other_count' => 0
        ];

        foreach ($orders as $order) {
            // 获取达人名称（从第一个订单中获取）
            if (empty($stats['author_name']) && isset($order->sku_order_list)) {
                foreach ($order->sku_order_list as $sku) {
                    if (isset($sku->author_id) && $sku->author_id == $authorId) {
                        $stats['author_name'] = $sku->author_name ?? '';
                        break;
                    }
                }
            }

            // 累计金额
            if (isset($order->order_amount)) {
                $stats['total_amount'] += $order->order_amount;
            }

            // 统计订单状态
            $status = $order->order_status_desc ?? '未知状态';
            if (!isset($stats['status_breakdown'][$status])) {
                $stats['status_breakdown'][$status] = 0;
            }
            $stats['status_breakdown'][$status]++;

            // 分类统计
            $statusLower = strtolower($status);
            if (strpos($statusLower, '待使用') !== false || strpos($statusLower, '待') !== false) {
                $stats['unused_count']++;
            } elseif (strpos($statusLower, '使用') !== false || strpos($statusLower, '完成') !== false) {
                $stats['used_count']++;
            } elseif (strpos($statusLower, '关闭') !== false || strpos($statusLower, '取消') !== false) {
                $stats['closed_count']++;
            } else {
                $stats['other_count']++;
            }
        }

        // 转换金额为元
        $stats['total_amount'] = $stats['total_amount'] / 100;

        return $stats;
    }

    /**
     * 记录操作日志
     */
    private function logOperation($operation, $data, $success, $error = null)
    {
        $logData = [
            'operation' => $operation,
            'timestamp' => date('Y-m-d H:i:s'),
            'success' => $success,
            'data' => $data
        ];

        if ($error) {
            $logData['error'] = $error;
        }

        error_log("OrderService: " . json_encode($logData, JSON_UNESCAPED_UNICODE));
    }
}
