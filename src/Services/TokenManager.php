<?php

require_once __DIR__ . '/../../sdk-php/src/autoload.php';

/**
 * Token管理类
 * 负责获取和刷新抖店API的access_token
 */
class TokenManager
{
    private $config;
    private $tokenFile;
    private $debugLogger;

    public function __construct($config)
    {
        $this->config = $config;
        $this->tokenFile = __DIR__ . '/../../storage/tokens.json';

        // 初始化调试器
        if (class_exists('DebugLogger')) {
            $this->debugLogger = new DebugLogger($config);
        }

        // 确保存储目录存在
        $storageDir = dirname($this->tokenFile);
        if (!is_dir($storageDir)) {
            mkdir($storageDir, 0755, true);
        }

        $this->debug('TokenManager Initialized', [
            'app_key' => substr($this->config['douyin']['app_key'], 0, 10) . '...',
            'api_base_url' => $this->config['douyin']['api_base_url']
        ]);

        // 设置SDK配置
        $globalConfig = GlobalConfig::getGlobalConfig();
        $globalConfig->appKey = $this->config['douyin']['app_key'];
        $globalConfig->appSecret = $this->config['douyin']['app_secret'];
        $globalConfig->openRequestUrl = $this->config['douyin']['api_base_url'];

        // 注册自动加载
        spl_autoload_register(['autoload', 'loadClass']);
    }

    /**
     * 调试日志记录
     */
    private function debug($message, $data = [])
    {
        if ($this->debugLogger) {
            $this->debugLogger->debug('[TokenManager] ' . $message, $data);
        }
    }

    /**
     * 通过授权码获取refresh_token
     */
    public function createToken($code, $shopId = null)
    {
        try {
            $request = new TokenCreateRequest();
            $param = new TokenCreateParam();

            $param->code = $code;
            $param->grant_type = 'authorization_code';

            if ($shopId) {
                $param->shop_id = $shopId;
            }

            $request->setParam($param);

            // 执行请求，这里不需要access_token
            $response = $request->execute(null);

            if ($response && $response->code == 10000) {
                $tokenData = [
                    'access_token' => $response->data->access_token,
                    'refresh_token' => $response->data->refresh_token,
                    'expires_in' => $response->data->expires_in,
                    'expires_at' => time() + $response->data->expires_in,
                    'scope' => $response->data->scope ?? '',
                    'shop_id' => $response->data->shop_id ?? $shopId,
                    'created_at' => time(),
                    'updated_at' => time()
                ];

                $this->saveToken($tokenData);
                $this->logOperation('create_token', $tokenData, true);

                return $tokenData;
            } else {
                $error = $response ? (isset($response->message) ? $response->message : '未知错误') : '未知错误';
                $this->logOperation('create_token', ['code' => $code], false, $error);
                throw new Exception("获取token失败: " . $error);
            }
        } catch (Exception $e) {
            $this->logOperation('create_token', ['code' => $code], false, $e->getMessage());
            throw $e;
        }
    }

    /**
     * 刷新access_token
     */
    public function refreshToken($refreshToken = null)
    {
        try {
            if (!$refreshToken) {
                $tokenData = $this->getStoredToken();
                if (!$tokenData || !isset($tokenData['refresh_token'])) {
                    throw new Exception("没有可用的refresh_token");
                }
                $refreshToken = $tokenData['refresh_token'];
            }

            $request = new TokenRefreshRequest();
            $param = new TokenRefreshParam();

            $param->refresh_token = $refreshToken;
            $param->grant_type = 'refresh_token';

            $request->setParam($param);

            // 执行请求
            $response = $request->execute(null);

            if ($response && $response->code == 10000) {
                $newTokenData = [
                    'access_token' => $response->data->access_token,
                    'refresh_token' => $response->data->refresh_token,
                    'expires_in' => $response->data->expires_in,
                    'expires_at' => time() + $response->data->expires_in,
                    'scope' => $response->data->scope ?? '',
                    'shop_id' => $response->data->shop_id ?? '',
                    'updated_at' => time()
                ];

                // 合并原有数据
                $existingData = $this->getStoredToken();
                if ($existingData) {
                    $newTokenData = array_merge($existingData, $newTokenData);
                }

                $this->saveToken($newTokenData);
                $this->logOperation('refresh_token', $newTokenData, true);

                return $newTokenData;
            } else {
                $error = $response ? (isset($response->message) ? $response->message : '未知错误') : '未知错误';
                $this->logOperation('refresh_token', ['refresh_token' => $refreshToken], false, $error);
                throw new Exception("刷新token失败: " . $error);
            }
        } catch (Exception $e) {
            $this->logOperation('refresh_token', ['refresh_token' => $refreshToken], false, $e->getMessage());
            throw $e;
        }
    }

    /**
     * 通过店铺ID直接获取token（无需用户授权）
     */
    public function createTokenByShopId($shopId = null)
    {
        try {
            if (!$shopId) {
                $shopId = $this->config['douyin']['shop_id'];
            }

            $request = new TokenCreateRequest();
            $param = new TokenCreateParam();

            $param->shop_id = $shopId;
            $param->grant_type = 'authorization_self';
            $param->code = '';

            $request->setParam($param);

            // 执行请求
            $response = $request->execute(null);

            if ($response && $response->code == 10000) {
                $tokenData = [
                    'access_token' => $response->data->access_token,
                    'refresh_token' => $response->data->refresh_token,
                    'expires_in' => $response->data->expires_in,
                    'expires_at' => time() + $response->data->expires_in,
                    'scope' => $response->data->scope ?? '',
                    'shop_id' => $response->data->shop_id ?? $shopId,
                    'created_at' => time(),
                    'updated_at' => time()
                ];

                $this->saveToken($tokenData);
                $this->logOperation('create_token_by_shop_id', $tokenData, true);

                return $tokenData;
            } else {
                $error = $response ? (isset($response->message) ? $response->message : '未知错误') : '未知错误';
                $this->logOperation('create_token_by_shop_id', ['shop_id' => $shopId], false, $error);
                throw new Exception("通过店铺ID获取token失败: " . $error);
            }
        } catch (Exception $e) {
            $this->logOperation('create_token_by_shop_id', ['shop_id' => $shopId], false, $e->getMessage());
            throw $e;
        }
    }

    /**
     * 获取有效的access_token，如果即将过期则自动刷新
     */
    public function getValidAccessToken($shopId = null)
    {
        if (!$shopId) {
            $shopId = $this->config['douyin']['shop_id'];
        }

        $this->debug('Getting Valid Access Token', ['shop_id' => $shopId]);

        $tokenData = $this->getStoredToken($shopId);

        if (!$tokenData) {
            $this->debug('No Token Found, Attempting Auto Creation', ['shop_id' => $shopId]);
            // 尝试通过店铺ID自动获取token
            try {
                $this->logOperation('auto_create_token', ['shop_id' => $shopId], true, '没有token，尝试自动获取');
                $tokenData = $this->createTokenByShopId($shopId);
                $this->debug('Auto Token Creation Successful', ['shop_id' => $shopId]);
            } catch (Exception $e) {
                $this->debug('Auto Token Creation Failed', ['shop_id' => $shopId, 'error' => $e->getMessage()]);
                throw new Exception("没有可用的token，自动获取失败: " . $e->getMessage());
            }
        }

        // 检查是否需要刷新token
        $threshold = $this->config['system']['token_refresh_threshold'] ?? 300;
        $currentTime = time();
        $expiresAt = $tokenData['expires_at'];
        $timeToExpire = $expiresAt - $currentTime;

        $this->debug('Token Expiry Check', [
            'shop_id' => $shopId,
            'current_time' => $currentTime,
            'expires_at' => $expiresAt,
            'time_to_expire' => $timeToExpire,
            'threshold' => $threshold,
            'needs_refresh' => $timeToExpire <= $threshold
        ]);

        if ($timeToExpire <= $threshold) {
            $this->debug('Token Needs Refresh', ['shop_id' => $shopId, 'time_to_expire' => $timeToExpire]);
            $this->logOperation('auto_refresh_check', ['expires_at' => $tokenData['expires_at']], true, '即将过期，开始刷新');
            $tokenData = $this->refreshToken($tokenData['refresh_token']);
            $this->debug('Token Refresh Completed', ['shop_id' => $shopId]);
        } else {
            $this->debug('Token Still Valid', ['shop_id' => $shopId, 'time_to_expire' => $timeToExpire]);
        }

        return $tokenData['access_token'];
    }

    /**
     * 获取AccessToken对象（SDK需要的格式）
     */
    public function getAccessTokenObject($shopId = null)
    {
        $accessTokenStr = $this->getValidAccessToken($shopId);
        return AccessTokenBuilder::parse($accessTokenStr);
    }

    /**
     * 获取店铺专用的token文件路径
     */
    private function getTokenFileForShop($shopId = null)
    {
        if (!$shopId) {
            $shopId = $this->config['douyin']['shop_id'];
        }

        $storageDir = dirname($this->tokenFile);
        return $storageDir . '/tokens_' . $shopId . '.json';
    }

    /**
     * 保存token到文件
     */
    private function saveToken($tokenData, $shopId = null)
    {
        $tokenFile = $this->getTokenFileForShop($shopId ?? $tokenData['shop_id'] ?? null);
        $json = json_encode($tokenData, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
        if (file_put_contents($tokenFile, $json) === false) {
            throw new Exception("保存token失败");
        }
    }

    /**
     * 从文件读取token
     */
    private function getStoredToken($shopId = null)
    {
        $tokenFile = $this->getTokenFileForShop($shopId);

        if (!file_exists($tokenFile)) {
            return null;
        }

        $json = file_get_contents($tokenFile);
        if ($json === false) {
            return null;
        }

        return json_decode($json, true);
    }

    /**
     * 记录操作日志
     */
    private function logOperation($operation, $data, $success, $error = null)
    {
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'operation' => $operation,
            'success' => $success,
            'data' => $data,
            'error' => $error
        ];

        $logFile = $this->config['system']['log_file'] ?? __DIR__ . '/../../logs/app.log';
        $logDir = dirname($logFile);

        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }

        $logLine = json_encode($logData, JSON_UNESCAPED_UNICODE) . "\n";
        file_put_contents($logFile, $logLine, FILE_APPEND | LOCK_EX);
    }

    /**
     * 获取授权URL
     */
    public function getAuthUrl($state = null)
    {
        $params = [
            'client_key' => $this->config['douyin']['app_key'],
            'response_type' => 'code',
            'scope' => 'item.list,order.list,afterSale.list,afterSale.operate',
            'redirect_uri' => $this->config['douyin']['redirect_uri'],
            'state' => $state ?? uniqid()
        ];

        return 'https://fxg.jinritemai.com/login/common/login/?' . http_build_query($params);
    }
}